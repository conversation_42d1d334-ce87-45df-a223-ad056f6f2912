#### 一级分类接口
{}
{
  "code": 0,
  "data":['Suits','Jackets']
}

#### filter接口
	{
		category:'Suits',//一级分类
		sed_category:'Ties',//二级分类
	}
	{
		code:0,
		data:{
			color:['Black','Navy'],
			price:['980-1679CNY','1679-3200CNY'],
			season:['Spring','Summer','Autumn','Winter'],
			material:['Cotton','Wool','Polyester'],
			size:['M','L','XL'],
			style:['Casual','Formal'],
			}
	}





#### 商品搜索接口
{
	goods_sn:'C25120A07',
	page:1,
	page_size:10,
	in_store_stock:1,//勾选为1，只有传1需要判断库存
	category:'Suits',//一级分类
	sed_category:'Ties',//二级分类
	sort_by:'1',//1:价格降序，2:价格升序，3:库存降序，4:库存升序
	filter:{
		color:['Black','Navy'],
		price:['980-1679CNY'],
		season:['Spring','Summer','Autumn','Winter'],
	}
}
{
	code:0,
	data:{
		page:1,
		page_size:10,
		total:100,
		list:[
				{
				goods_sn:'C25120A07',
				goods_name:'商品名称',
				goods_price:1000,
				goods_img:'http://xxx.com/xxx.jpg',
				discription:'商品描述',
				color:'黑色',
				}
			]
	}
}

#### 商品扫码查询接口
{serial_no:'C25120A07'}
{
	code:0,
	data:{
	  goods_sn:'C25120A07',
			goods_name:'商品名称',
			goods_price:1000,
			goods_img:'http://xxx.com/xxx.jpg',
			discription:'商品描述',
			color:'黑色',
			serial_no:'',
			size:'M',
			barcode:''
	}
}



#### 二级分类接口(前端自己加ALL)
{category:'Suits'}
{
  "code": 200,
  "data":['Ties','Socks']
}





#### 商品详情接口
{goods_sn:'C25120A07',color:'Black'}
{
	code:0,
	data:{
		goods_sn:'C25120A07',
		goods_name:'商品名称',
		goods_price:1000,
		goods_img:'http://xxx.com/xxx.jpg',
		discription:'商品描述',
		color:'黑色',
		serial_no:'',
		size:'M',
		barcode:'',
		detail:
			{
				'MasterProductId':'H7326A',
				'Material':'Egyptain Cotton'
			},
		detail_img:['imgUlr1','imgUlr2'],
		cm_url:'http://xxx.com/xxx.jpg',//custom 的url
		'裁剪url':'http://xxx.com/xxx.jpg'
	}
}

#### 商品尺码 (按照后端返回的顺序排序)
{goods_sn:'C25120A07',color:'Black'}
{
	code:0,
	data:[
		{size:'22',
		'barcode':'1xxxxx',
		store_stock:10,
		network_stock:10,
		cm_url:'http://xxx.com/xxx.jpg',//custom 的url
		},
		{size:'23',
		'barcode':'1xxxxx',
		store_stock:10,
		network_stock:10,
		cm_url:'http://xxx.com/xxx.jpg',//custom 的url
		}]
}




#### 临店库存接口
{barcode:'1xxxxx'}
{
	code:0,
	data:{
		code:1,
		name:'临店名称',
		address:'',
		mobile:'',
		store_stock:10
	}
}



#### 地址列表
{uid:1}
{
	code:0,
	data:[
		{
			uid:1,
			name:'张三',
			mobile:'138xxxx',
			country:'中国',
			province:'北京市',
			city:'北京市',
			district:'朝阳区',
			detail_address:'xxx街道',
			id:''//地址主键
		},
		{
			uid:1,
			name:'李四',
			mobile:'138xxxx',
			country:'中国',
			province:'北京市',
			city:'北京市',
			district:'朝阳区',
			detail_address:'xxx街道',
			id:''//地址主键
		}
	]
}

#### 添加地址
{
			uid:1,
			name:'李四',
			mobile:'138xxxx',
			country:'中国',
			province:'北京市',
			city:'北京市',
			district:'朝阳区',
			detail_address:'xxx街道',
		}
{
	code:0,
	data:{
		
	}
}

#### 修改地址
{
			uid:1,
			name:'李四',
			mobile:'138xxxx',
			country:'中国',
			province:'北京市',
			city:'北京市',
			district:'朝阳区',
			detail_address:'xxx街道',
			id:''//地址主键
		}
{
	code:0,
	data:{
	}
}

#### 删除地址
{
	uid:'',
	id:''//地址主键
}
{
  code:0,
}





#### OrderHistory列表
{
	uid:1,
	page:1,
	page_size:10,
	search_input:'',//epc barcode 订单号
	start_date:'2019-09-09',//开始时间
	end_date:'2019-09-09',//结束时间
}
{
	code:0,
	data:{
		page:1,
		page_size:10,
		total:100,
		list:[
				{
				order_sn:'C25120A07',
				order_time:'2019-09-09 09:09:09',
				order_status:'已发货',
				order_price:1000,
				order_qty:4,
				custom:'Li,jindou',
				goods:[
					goods_sn:'C25120A07',
					goods_name:'商品名称',
					goods_price:1000,
					goods_img:'http://xxx.com/xxx.jpg',
					discription:'商品描述',
					color:'黑色',
					serial_no:'',
					size:'M',
					barcode:'',
					status:'',单个商品状态
					detail:
						{
							'MasterProductId':'H7326A',
							'Material':'Egyptain Cotton'
						},
					detail_img:['imgUlr1','imgUlr2'],
					cm_url:'http://xxx.com/xxx.jpg',//custom 的url
					'裁剪url':'http://xxx.com/xxx.jpg']
				}]
	}
}