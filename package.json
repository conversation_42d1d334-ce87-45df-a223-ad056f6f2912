{"name": "pos_suitsupply", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"build": "node scripts/build.js", "start": "node scripts/start.js", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/icons-react-taro": "^2.0.1", "@nutui/nutui-react-taro": "^2.7.8", "@taroify/core": "^0.1.1-alpha.11", "@tarojs/components": "3.6.4", "@tarojs/helper": "3.6.4", "@tarojs/plugin-framework-react": "3.6.4", "@tarojs/plugin-html": "3.6.4", "@tarojs/plugin-inject": "^4.0.9", "@tarojs/plugin-mock": "^0.0.9", "@tarojs/plugin-platform-alipay": "3.6.4", "@tarojs/plugin-platform-h5": "3.6.4", "@tarojs/plugin-platform-jd": "3.6.4", "@tarojs/plugin-platform-qq": "3.6.4", "@tarojs/plugin-platform-swan": "3.6.4", "@tarojs/plugin-platform-tt": "3.6.4", "@tarojs/plugin-platform-weapp": "3.6.4", "@tarojs/react": "3.6.4", "@tarojs/runtime": "3.6.4", "@tarojs/shared": "3.6.4", "@tarojs/taro": "3.6.4", "antd": "^5.23.1", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "3.6.4", "classnames": "^2.5.1", "dayjs": "^1.11.13", "install": "^0.13.0", "js-md5": "^0.8.3", "lodash": "^4.17.21", "mobx": "^6.11.0", "mobx-persist-store": "^1.1.3", "mobx-react": "^9.1.0", "npm": "^11.1.0", "numeral": "^2.0.6", "react": "^18.0.0", "react-dom": "^18.0.0", "shelljs": "^0.8.5", "taro-iconfont-cli": "^3.3.0", "taro-react-echarts": "^1.2.2"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.4", "@tarojs/webpack5-runner": "3.6.4", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "autoprefixer": "^10.4.14", "babel-preset-taro": "3.6.4", "eslint": "^8.12.0", "eslint-config-taro": "3.6.4", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "tailwindcss": "^3.3.0", "typescript": "^4.1.0", "webpack": "5.94.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}