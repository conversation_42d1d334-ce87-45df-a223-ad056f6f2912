export default defineAppConfig({
  pages: [
    "pages/home/<USER>", // 工作台
    "pages/login/index", //登录
    "pages/setting/index", // 工作台
    "pages/example/index",
    "pages/example/mock-demo", // Taro Mock 测试页面
    "pages/webview/microsoft-auth/index", // 小程序微软登录web-view页面
  ],
  subpackages: [
    {
      //商城
      root: "subpackages/mall",
      pages: ["catalog/index", "index/index", "custom-made/index", "plp/index", "pdp/index","order-history/index",'filters/index'],
    },
    // 顾客
    {
      root: "subpackages/customer",
      pages: ["index/index", "detail/index", "registration/index", "customerSelect/index", "edit/index", "modifyAddress/index", "editAddress/index"],
    },
  ],
  window: {
    backgroundColor: "#fff",
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "SuitSupply",
    navigationBarTextStyle: "black",
    onReachBottomDistance: 200,
  },
  requiredPrivateInfos: [],
  permission: {
    "scope.bluetooth": {
      desc: "需要蓝牙权限连接打印机",
    },
    "scope.bluetoothAdjacent": {
      desc: "需要蓝牙设备通信权限",
    },
  },
  usingComponents: Object.assign({
    iconfont: `components/iconfont/${process.env.TARO_ENV}/${process.env.TARO_ENV}`,
  }),
});
