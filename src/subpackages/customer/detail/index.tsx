import { View, Textarea } from "@tarojs/components";
import { MainLayout, OrderCard, SafeAreaBottom } from "@/components";
import { Divider, Radio, Checkbox } from "@nutui/nutui-react-taro";
import IconFont from "@/components/iconfont";
import CustomerCard from "../components/CustomerCard";
import { useI18n } from "@/hook";
import sty from "./index.module.scss";
import { observer } from "mobx-react";
import { useStores } from "@/hook";
import { useEffect, useState } from "react";
import { useDidShow, useRouter } from "@tarojs/taro";

import http from "@/http";
import Taro from "@tarojs/taro";
import { toJump } from "@/utils";

const Detail = () => {
  const router = useRouter();
  const { params } = router;

  const { t } = useI18n();
  const { customerDetail, toastStore, customerStore } = useStores();
  const { currentCustomer } = customerStore;
  const [noteDisabled, setNoteDisabled] = useState(true);
  const [note, setNote] = useState<string>(customerDetail.detail.note ?? "");
  const [newsletterShow, setNewsletterShow] = useState(false);
  const [receiptShow, setReceiptShow] = useState(false);

  useDidShow(() => {
    if (!params.mobile) return;
    customerDetail.getCustomDetail(params.mobile);
  });

  useEffect(() => {
    setNote(customerDetail.detail.note ?? "");
  }, [customerDetail.detail.note]);

  const onEditNotes = async (e) => {
    setNoteDisabled(true);
    const newNote = e.detail.value;
    customerDetail.detail.note = newNote; // 更新customerDetail中的备注
    if (newNote !== note) {
      const res: any = await http.customer.updateMemberInfo({
        note: newNote,
        mobile: params.mobile,
      });
      if (res.code == 0) {
        setNote(newNote);
        toastStore.show(res.msg);
        // 如果当前客户已经check-in，同时更新currentCustomer中的备注
        if (currentCustomer && currentCustomer.mobile === params.mobile) {
          customerStore.setCurrentCustomer({
            ...currentCustomer,
            note: newNote,
          });
        }
      } else {
        toastStore.show({ icon: "error", content: res.msg });
      }
    }
  };

  const onCheckIn = () => {
    if (!Object.keys(currentCustomer).length) {
      customerStore.setCurrentCustomer(customerDetail.detail);
    } else {
      customerStore.reset();
    }
    toJump("/pages/home/<USER>");
  };

  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      // 使用便捷配置方式
      headerType="withBack"
      headerBackgroundColor="#FAFAFA"
      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#FAFAFA",
      }}
      showBottomPlaceholder={false}>
      <View className={[sty.detailPage].join(" ")}>
        <View className={sty.content}>
          <View className="border-1-area-xs bg-white ">
            <CustomerCard info={customerDetail.detail as any} />

            {Object.keys(currentCustomer).length > 0 && (
              <>
                <Divider className="mt-2 mb-10 px-8 box-border " />
                <View
                  className="flex items-center justify-between px-8 pb-8"
                  onClick={() => {
                    toJump("subpackages/customer/index/index");
                  }}>
                  <View className="text-base leading-10 text-brand-2D2E2C">{t("customer.switchCustomer")}</View>
                  <View className="flex items-center text-sm leading-8 text-brand-2D2E2C ">
                    <View>{t("customer.view")}</View>
                    <IconFont name="detail" size={20} />
                  </View>
                </View>
              </>
            )}
          </View>

          {/* 备注 */}

          <View className="border-1-area-xs py-10 px-8 mt-6 bg-white">
            <View className="flex items-center justify-between">
              <View className="flex items-center">
                <IconFont name="note" size={20} />
                <View className="ml-2 text-base text-brand-2D2E2C">{t("customer.notes")}</View>
              </View>
              <View className="border-1-area flex justify-center items-center w-40 h-12 rounded-14" onClick={() => setNoteDisabled(false)}>
                <IconFont name="bianji" size={12} />
                <View className="text-sm  ml-1">{t("customer.editNote")}</View>
              </View>
            </View>

            <View className="mt-4 border-1-area-xs">
              <Textarea
                className={`w-full h-40 py-4 px-6 box-border leading-8 text-brand-2D2E2C bg-brand-FAFAFA`}
                disabled={noteDisabled}
                value={note}
                cursor={note.length}
                focus={!noteDisabled}
                onBlur={onEditNotes}
              />
            </View>
          </View>

          {/* 地址 */}

          <View className="border-1-area-xs py-10 px-8 mt-6 bg-white">
            <View
              className="flex items-center justify-between"
              onClick={() => {
                Taro.navigateTo({
                  url: "/subpackages/customer/modifyAddress/index",
                });
              }}>
              <View className="flex items-center">
                <IconFont name="address" size={20} />
                <View className="ml-2 text-base text-brand-2D2E2C">{t("customer.address")}</View>
              </View>
              <IconFont name="detail" size={20} />
            </View>
            <View className="mt-4 border-1-area-xs box-border">
              <View className={`w-full h-52 py-4 px-6 box-border text-sm leading-8 text-brand-2D2E2C bg-brand-FAFAFA`}>
                <View className="font-medium ">Wang, Jason</View>
                <View className="mt-2">18812466625</View>
                <View className="text-brand-898989 mt-6">XuHui District,Shanghai 169 Anfu Road XuHui District</View>
              </View>
            </View>
          </View>

          <View className="border-1-area-xs py-10 pl-8 mt-6 bg-white">
            <View className="flex items-center justify-between pr-6">
              <View className="flex items-center">
                <IconFont name="order" size={20} />

                <View className="ml-2 text-base text-brand-2D2E2C">{t("customer.orderHistory")}</View>
              </View>
              <IconFont name="detail" size={20} />
            </View>
            <View className="mt-5 w-full overflow-x-auto">
              <OrderCard {...(customerDetail.oh || {})} class="mx-5" />
            </View>
          </View>

          {/* <View className="border-1-area-xs py-10 px-8 mt-6 text-base text-brand-2D2E2C">
          <View className="flex justify-between h-10 pb-8">
            <View className="flex items-center ">
              <IconFont name="timeLine" size={24} />
              <View className="ml-3  ">{t("customer.timeline")}</View>
            </View>
            <View className="size-10">
              <IconFont name="detail" size={20} />
            </View>
          </View>
          <View className="flex justify-between h-10 py-8 border-1-area-xs rounded-none border-x-0 ">
            <View className="flex items-center ">
              <IconFont name="rules" size={24} />
              <View className="ml-3  ">{t("customer.sizePassport")}</View>
            </View>
            <View className="size-10">
              <IconFont name="detail" size={20} />
            </View>
          </View>
          <View className="flex justify-between h-10 pt-8">
            <View className="flex items-center ">
              <IconFont name="customMade" size={24} />
              <View className="ml-3  ">{t("customer.customization")}</View>
            </View>
            <View className="size-10">
              <IconFont name="detail" size={20} />
            </View>
          </View>
        </View> */}

          <View className="border-1-area-xs py-10 px-8 mt-6 text-base text-brand-2D2E2C bg-white">
            <View className="flex  items-center h-10 pb-8">
              <Checkbox icon={<IconFont name="checkbox1" size={16} />} activeIcon={<IconFont name="checked1" size={16} />} checked={newsletterShow} onChange={setNewsletterShow}>
                <View className="ml-3  ">{t("customer.timeline")}</View>
              </Checkbox>
            </View>
            <View className={["flex items-center  h-10 pt-8 border-t-1-xs", sty.radioRow].join()}>
              <Checkbox icon={<IconFont name="checkbox1" size={16} />} activeIcon={<IconFont name="checked1" size={16} />} checked={receiptShow} onChange={setReceiptShow}>
                <View className="ml-3  ">{t("customer.sizePassport")}</View>
              </Checkbox>
            </View>
          </View>
        </View>
        <View className={sty.btnArea}>
          <View className={sty.btn} onClick={onCheckIn}>
            {Object.keys(currentCustomer).length ? "Logout" : "Check-In"}
          </View>
          <SafeAreaBottom />
        </View>
      </View>
    </MainLayout>
  );
};

export default observer(Detail);
