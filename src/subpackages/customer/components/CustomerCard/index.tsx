import { View } from "@tarojs/components"
import { Image } from "@nutui/nutui-react-taro"
import IconFont from "@/components/iconfont"
import { toJump } from "@/utils"

interface infoType {
  avatar:string;
  name:string
  mobile:string;
  email:string;
}
interface CustomerCardType {
  info: infoType
}
const CustomerCard:React.FC<CustomerCardType> = ({info}) => {
  return (
    <View className="customerCardCom p-8 border-1-area-xs flex">
      <View className="size-24 mr-5">
        <Image radius={'50%'} src={info.avatar}/>
      </View>
      <View>
        <View className="leading-10 text-base font-medium text-brand-2D2E2C flex items-center" >
          <View>{info.name}</View>
          <View className="ml-2" onClick={()=>{
            toJump('subpackages/customer/edit/index');
          }}>
            <IconFont name="bianji" size={16}/>
          </View>

        </View>
        <View className="mt-4 text-sm leading-8 text-brand-707070">Phone Number: {info.mobile}</View>
        <View className="mt-2 text-sm leading-8 text-brand-707070">Email: {info.email}</View>
      </View>
    </View>
  )
}


export default CustomerCard
