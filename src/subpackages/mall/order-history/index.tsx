import { View, ScrollView, Image } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
// import { Image } from "@nutui/nutui-react-taro";

import { MainLayout, SearchInput } from "@/components";
import IconFont from "@/components/iconfont";
import http from "@/http";
import { useStores } from "@/hook";

// 订单数据类型定义
interface OrderProduct {
  id: string;
  name: string;
  image: string;
  price: number;
  quantity: number;
}

interface OrderItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  orderTime: string;
  status: string;
  products: OrderProduct[];
  totalQuantity: number;
  totalAmount: number;
  isExchange: boolean;
}

const OrderHistoryPage = () => {
  const { loadingStore } = useStores();
  const [searchValue, setSearchValue] = useState("");
  const [orderList, setOrderList] = useState<OrderItem[]>([]);

  // 模拟订单数据
  const mockOrderData: OrderItem[] = [
    {
      id: "1",
      orderNumber: "PS00048229",
      customerName: "Li, Jindou",
      customerPhone: "18812436614",
      orderTime: "2025-04-22 09:29:58",
      status: "Completed",
      isExchange: true,
      products: [
        {
          id: "1",
          name: "White Blazer",
          image: "https://cdn.suitsupply.com/image/upload/w_120,h_144,c_lfill,g_center,b_rgb:efefef,f_auto//suitsupply/campaigns/ss25/quicklinks/polos/polos_t-shirts_short-sleeve-polos.jpg",
          price: 3000,
          quantity: 1,
        },
        {
          id: "2",
          name: "White Shirt",
          image: "https://cdn.suitsupply.com/image/upload/w_120,h_144,c_lfill,g_center,b_rgb:efefef,f_auto//suitsupply/campaigns/ss25/quicklinks/polos/polos_t-shirts_short-sleeve-polos.jpg",
          price: 800,
          quantity: 1,
        },
        {
          id: "3",
          name: "Gray Suit",
          image: "https://cdn.suitsupply.com/image/upload/w_120,h_144,c_lfill,g_center,b_rgb:efefef,f_auto//suitsupply/campaigns/ss25/quicklinks/polos/polos_t-shirts_short-sleeve-polos.jpg",
          price: 8000,
          quantity: 1,
        },
        {
          id: "4",
          name: "White Pants",
          image: "https://cdn.suitsupply.com/image/upload/w_120,h_144,c_lfill,g_center,b_rgb:efefef,f_auto//suitsupply/campaigns/ss25/quicklinks/polos/polos_t-shirts_short-sleeve-polos.jpg",
          price: 279,
          quantity: 1,
        },
        {
          id: "4",
          name: "White Pants",
          image: "https://cdn.suitsupply.com/image/upload/w_120,h_144,c_lfill,g_center,b_rgb:efefef,f_auto//suitsupply/campaigns/ss25/quicklinks/polos/polos_t-shirts_short-sleeve-polos.jpg",
          price: 279,
          quantity: 1,
        },
      ],
      totalQuantity: 4,
      totalAmount: 12079,
    },
  ];

  useEffect(() => {
    // 初始化加载订单数据
    loadOrderHistory();
  }, []);

  const loadOrderHistory = async () => {
    const loadingUUID = loadingStore.open({
      isFullScreen: true,
      showDelay: 0,
    });

    try {
      const response: any = await http.order.getOrderHistory({
        uid: "current_user_id", // 可以从store中获取当前用户ID
        page: 1,
        page_size: 20,
        search_input: searchValue || "",
        start_date: "",
        end_date: ""
      });

      if (response.code === 0) {
        // 转换sit2 API数据格式为组件需要的格式
        const formattedOrders: OrderItem[] = response.data.list.map((item: any) => ({
          id: item.order_sn,
          orderNumber: item.order_sn,
          customerName: item.custom,
          customerPhone: "", // sit2接口中没有电话号码，可能需要从客户详情中获取
          orderTime: item.order_time,
          status: item.order_status,
          products: item.goods.map((good: any) => ({
            id: good.goods_sn,
            name: good.goods_name,
            image: good.goods_img,
            price: good.goods_price,
            quantity: 1 // sit2接口中没有单个商品数量，默认为1
          })),
          totalQuantity: item.order_qty,
          totalAmount: item.order_price,
        }));
        setOrderList(formattedOrders);
      } else {
        // 如果API失败，使用模拟数据作为fallback
        setOrderList(mockOrderData);
      }
    } catch (error) {
      console.error("加载订单历史失败:", error);
      // 出错时使用模拟数据
      setOrderList(mockOrderData);
    } finally {
      loadingStore.close(loadingUUID);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    // TODO: 实现搜索逻辑
    console.log("search change:", value);
  };

  const handleRetrieve = async (order: OrderItem) => {
    try {
      const response: any = await http.order.retrieveOrder({
        order_id: order.id,
      });

      if (response.code === 0) {
        Taro.showToast({
          title: "订单检索成功",
          icon: "success",
          duration: 2000,
        });
        // 可以在这里添加跳转到购物车的逻辑
        // Taro.navigateTo({ url: '/pages/cart/index' });
      } else {
        Taro.showToast({
          title: response.message || "检索失败",
          icon: "error",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("检索订单失败:", error);
      Taro.showToast({
        title: "检索失败，请重试",
        icon: "error",
        duration: 2000,
      });
    }
  };

  const renderOrderItem = (order: OrderItem) => {
    return (
      <View key={order.id} className="bg-white border-1-area mb-6 py-10 px-6">
        {/* 订单头部信息 */}
        <View className="flex-between mb-2">
          <View className="text-sm leading-8 font-medium text-brand-2D2E2C mb-1">Orders: #PS00048203</View>
          <View className="text-brand-898989 text-sm flex-center leading-8 font-medium">
            {order.status} <IconFont name="detail"></IconFont>
          </View>
        </View>
        <View className="flex-between mb-8">
          <View className="text-sm text-brand-898989 leading-8">{order.orderTime}</View>
          {order.isExchange && <View className="text-sm text-brand-C33333 leading-8">Exchange</View>}
        </View>

        {/* 产品图片列表 */}
        <ScrollView className="w-full" scrollX enhanced showScrollbar={false} type="list">
          <View className="flex-between">
            {order.products.map((product) => (
              <Image src={product.image} className="w-70 h-94 mr-4" mode="widthFix" />
            ))}
          </View>
        </ScrollView>
        {/* 右侧遮罩层 */}
        <View>
          <View>
            <View className="text-sm text-brand-898989 mb-2">Qty.{order.totalQuantity}</View>
            <View className="text-2xl font-bold text-brand-2D2E2C">¥{order.totalAmount}</View>
          </View>
        </View>

        {/* 订单底部信息 */}
        <View className="flex justify-between items-end">
          <View className="bg-gray-900 text-white px-8 py-3 rounded-xl text-sm font-medium transition-colors" onClick={() => handleRetrieve(order)}>
            Customer:{order.customerName}
          </View>
        </View>
      </View>
    );
  };

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: false,
        loadingFullScreen: false,
      }}
      headerType="withBack"
      showBottomPlaceholder>
      {/* 固定的搜索区域 */}
      <View className="sticky top-0 z-50 border-b border-gray-200 pt-8 pb-4 bg-white">
        <View className="flex items-center gap-3">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="flex items-center">
                <View className="w-12 h-12 bg-gray-100 border-1-area flex items-center justify-center rounded-sm  ml-3">
                  <IconFont name="scan" color={"#666"} size={20} />
                </View>
                <View className="w-12 h-12 bg-gray-900 flex items-center justify-center rounded-sm  ml-3">
                  <IconFont name="scan" color={"#fff"} size={20} />
                </View>
              </View>
            }
          />
        </View>
      </View>

      {/* 订单列表区域 */}
      <ScrollView className="flex-1 bg-gray-50 mx-8" scrollY enhanced showScrollbar={false} type="list" style={{ height: "calc(100vh - 140px)" }}>
        <View>
          {orderList.length > 0 ? (
            orderList.map((order) => renderOrderItem(order))
          ) : (
            <View className="flex flex-col items-center justify-center mt-32">
              <View className="text-gray-400 text-lg mb-2">📋</View>
              <View className="text-brand-898989 text-base">暂无订单数据</View>
              <View className="text-gray-400 text-sm mt-1">请稍后再试或联系客服</View>
            </View>
          )}
        </View>
      </ScrollView>
    </MainLayout>
  );
};

export default observer(OrderHistoryPage);
