import { View, ScrollView } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import { Image } from "@nutui/nutui-react-taro";

import { MainLayout, SearchInput } from "@/components";
import IconFont from "@/components/iconfont";
import http from "@/http";
import { useStores } from "@/hook";

// 订单数据类型定义
interface OrderProduct {
  id: string;
  name: string;
  image: string;
  price: number;
  quantity: number;
}

interface OrderItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  orderTime: string;
  status: string;
  products: OrderProduct[];
  totalQuantity: number;
  totalAmount: number;
}

const OrderHistoryPage = () => {
  const { loadingStore } = useStores();
  const [searchValue, setSearchValue] = useState("");
  const [orderList, setOrderList] = useState<OrderItem[]>([]);

  // 模拟订单数据
  const mockOrderData: OrderItem[] = [
    {
      id: "1",
      orderNumber: "PS00048229",
      customerName: "Li, Jindou",
      customerPhone: "18812436614",
      orderTime: "2025-04-22 09:29:58",
      status: "Completed",
      products: [
        {
          id: "1",
          name: "White Blazer",
          image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Blazer",
          price: 3000,
          quantity: 1
        },
        {
          id: "2",
          name: "White Shirt",
          image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Shirt",
          price: 800,
          quantity: 1
        },
        {
          id: "3",
          name: "Gray Suit",
          image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Suit",
          price: 8000,
          quantity: 1
        },
        {
          id: "4",
          name: "White Pants",
          image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Pants",
          price: 279,
          quantity: 1
        }
      ],
      totalQuantity: 4,
      totalAmount: 12079
    }
  ];

  useEffect(() => {
    // 初始化加载订单数据
    loadOrderHistory();
  }, []);

  const loadOrderHistory = async () => {
    const loadingUUID = loadingStore.open({
      isFullScreen: true,
      showDelay: 0
    });

    try {
      const response: any = await http.order.getOrderHistory({
        page: 1,
        page_size: 20
      });

      if (response.code === 0) {
        // 转换API数据格式为组件需要的格式
        const formattedOrders: OrderItem[] = response.data.list.map((item: any) => ({
          id: item.id,
          orderNumber: item.order_number,
          customerName: item.customer_name,
          customerPhone: item.customer_phone,
          orderTime: item.order_time,
          status: item.status,
          products: item.products,
          totalQuantity: item.total_quantity,
          totalAmount: item.total_amount
        }));
        setOrderList(formattedOrders);
      } else {
        // 如果API失败，使用模拟数据作为fallback
        setOrderList(mockOrderData);
      }
    } catch (error) {
      console.error("加载订单历史失败:", error);
      // 出错时使用模拟数据
      setOrderList(mockOrderData);
    } finally {
      loadingStore.close(loadingUUID);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    // TODO: 实现搜索逻辑
    console.log("search change:", value);
  };

  const handleRetrieve = async (order: OrderItem) => {
    try {
      const response: any = await http.order.retrieveOrder({
        order_id: order.id
      });

      if (response.code === 0) {
        Taro.showToast({
          title: "订单检索成功",
          icon: "success",
          duration: 2000
        });
        // 可以在这里添加跳转到购物车的逻辑
        // Taro.navigateTo({ url: '/pages/cart/index' });
      } else {
        Taro.showToast({
          title: response.message || "检索失败",
          icon: "error",
          duration: 2000
        });
      }
    } catch (error) {
      console.error("检索订单失败:", error);
      Taro.showToast({
        title: "检索失败，请重试",
        icon: "error",
        duration: 2000
      });
    }
  };

  const renderOrderItem = (order: OrderItem) => {
    return (
      <View key={order.id} className="bg-white rounded-2xl border border-gray-200 mb-4 p-6 shadow-sm">
        {/* 订单头部信息 */}
        <View className="flex justify-between items-start mb-4">
          <View className="flex-1">
            <View className="text-lg font-medium text-gray-900 mb-1">
              Customer: {order.customerName}
            </View>
            <View className="text-sm text-gray-500">
              {order.customerPhone}
            </View>
          </View>
          <View className="text-right ml-4">
            <View className="text-sm text-gray-500">
              {order.orderTime}
            </View>
          </View>
        </View>

        {/* 产品图片列表 */}
        <View className="mb-6">
          <ScrollView
            className="w-full"
            scrollX
            enhanced
            showScrollbar={false}
            type="list"
          >
            <View className="flex gap-3 pb-2">
              {order.products.map((product) => (
                <View key={product.id} className="flex-shrink-0">
                  <Image
                    src={product.image}
                    className="w-28 h-36 rounded-xl bg-gray-100 border border-gray-100"
                    mode="aspectFill"
                  />
                </View>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* 订单底部信息 */}
        <View className="flex justify-between items-end">
          <View>
            <View className="text-sm text-gray-500 mb-2">
              Qty.{order.totalQuantity}
            </View>
            <View className="text-2xl font-bold text-gray-900">
              ¥{order.totalAmount}
            </View>
          </View>
          <View
            className="bg-gray-900 text-white px-8 py-3 rounded-xl text-sm font-medium active:bg-gray-800 transition-colors"
            onClick={() => handleRetrieve(order)}
          >
            Retrieve
          </View>
        </View>
      </View>
    );
  };

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: false,
        loadingFullScreen: false,
      }}
      headerType="withBack"
      showBottomPlaceholder
    >
      {/* 固定的搜索区域 */}
      <View className="sticky top-0 z-50 border-b border-gray-200 pt-8 pb-4 bg-white">
        <View className="flex items-center gap-3 px-4">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="flex items-center">
                <View className="w-12 h-12 bg-gray-100 border border-gray-200 flex items-center justify-center rounded-lg ml-3">
                  <IconFont name="scan" color={"#666"} size={20} />
                </View>
                <View className="w-12 h-12 bg-gray-900 flex items-center justify-center rounded-lg ml-3">
                  <IconFont name="scan" color={"#fff"} size={20} />
                </View>
              </View>
            }
          />
        </View>
      </View>

      {/* 订单列表区域 */}
      <ScrollView
        className="flex-1 bg-gray-50"
        scrollY
        enhanced
        showScrollbar={false}
        type="list"
        style={{ height: 'calc(100vh - 140px)' }}
      >
        <View className="p-4 pb-8">
          {orderList.length > 0 ? (
            orderList.map(order => renderOrderItem(order))
          ) : (
            <View className="flex flex-col items-center justify-center mt-32">
              <View className="text-gray-400 text-lg mb-2">📋</View>
              <View className="text-gray-500 text-base">暂无订单数据</View>
              <View className="text-gray-400 text-sm mt-1">请稍后再试或联系客服</View>
            </View>
          )}
        </View>
      </ScrollView>
    </MainLayout>
  );
};

export default observer(OrderHistoryPage);
