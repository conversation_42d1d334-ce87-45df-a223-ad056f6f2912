import { View } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import { Image } from "@nutui/nutui-react-taro";

import { MainLayout, SearchInput } from "@/components";
import { useI18n, useStores } from "@/hook";
import IconFont, { IconNames } from "@/components/iconfont";
const HomePage = () => {
  const { t } = useI18n();

  const handleSearchChange = () => {
    console.log("search change");
  };


	const venderOrderItem = ()=>{

	}
  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      // 使用便捷配置方式
      headerType="withBack"
      showBottomPlaceholder>
      {/* 固定的搜索区域 */}
      <View className="z-50 border-b border-gray-200 pt-8">
        <View className="flex items-center gap-3">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="flex-center">
                <View className="w-20 h-20 bg-brand-FAFAFA border-1-area flex-center rounded-sm ml-4">
                  <IconFont name="scan" color={"#000"} size={28} />
                </View>
                <View className="w-20 h-20 bg-black flex-center rounded-sm ml-4">
                  <IconFont name="scan" color={"#fff"} size={28} />
                </View>
              </View>
            }
          />
        </View>
      </View>

      {/* list区域 */}
      <View className="p-4">
				{
					venderOrderItem()
				}
			</View>
    </MainLayout>
  );
};

export default observer(HomePage);
