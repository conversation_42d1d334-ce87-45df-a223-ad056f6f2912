import { View, ScrollView, Image } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
// import { Image } from "@nutui/nutui-react-taro";

import { MainLayout, SearchInput, ErrorMessage, OrderCard } from "@/components";
import IconFont from "@/components/iconfont";
import http from "@/http";
import { useStores } from "@/hook";
import { customerStore, toastStore } from "@/mobx";
import { OrderItem, OrderGoods } from "@/mobx/model/Order";

// 订单数据类型定义 - 直接使用sit2接口字段名

interface DateMap {
  start_date: string;
  end_date: string;
}

const OrderHistoryPage = () => {
  const { loadingStore } = useStores();
  const [searchValue, setSearchValue] = useState("");
  const [orderList, setOrderList] = useState<OrderItem[]>([]);
  const [errorMessage, setErrorMessage] = useState<string>("No order history");
  const [errorIcon, setErrorIcon] = useState<string>("none");
  const [filterDate, setFilterDate] = useState<DateMap>({ start_date: "", end_date: "" });

  useEffect(() => {
    // 初始化加载订单数据
    loadOrderHistory();
  }, []);

  const loadOrderHistory = async () => {
    const loadingUUID = loadingStore.open({
      isFullScreen: true,
      showDelay: 0,
    });

    try {
      const response: any = await http.order.getOrderHistory({
        uid: customerStore.currentCustomer?.uid, // 可以从store中获取当前用户ID
        page: 1,
        page_size: 10,
        search_input: searchValue || "",
        start_date: filterDate.start_date || "",
        end_date: filterDate.end_date || "",
      });

      if (response.code === 0 && response.data.list.length > 0) {
        setOrderList(response.data.list);
      } else {
        setErrorMessage("No matching order found");
        setErrorIcon("error");
      }
    } catch (error) {
      setErrorMessage("No matching order found");
      setErrorIcon("error");
    } finally {
      loadingStore.close(loadingUUID);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    // TODO: 实现搜索逻辑
    loadOrderHistory();
    console.log("search change:", value);
  };

  const handleRetrieve = async (order: OrderItem) => {
    try {
      const response: any = await http.order.retrieveOrder({
        order_id: order.order_sn,
      });

      if (response.code === 0) {
      } else {
        setErrorMessage("No matching order found");
      }
    } catch (error) {
      setErrorMessage("No matching order found");
    }
  };

  const renderOrderItem = (order: OrderItem) => {
    return (
      <View key={order.order_sn} className="bg-white border-1-area mb-6 py-10 px-6">
        {/* 订单头部信息 */}
        <View className="flex-between mb-2">
          <View className="text-sm leading-8 font-medium text-brand-2D2E2C mb-1">Orders: #{order.order_sn}</View>
          <View className="text-brand-898989 text-sm flex-center leading-8 font-medium">
            {order.order_status} <IconFont name="detail"></IconFont>
          </View>
        </View>
        <View className="flex-between mb-8">
          <View className="text-sm text-brand-898989 leading-8">{order.order_time}</View>
          <View className="text-sm text-brand-C33333 leading-8">{order.order_status}</View>
        </View>

        {/* 产品图片列表 */}
        <ScrollView scrollX enhanced showScrollbar={false} type="list">
          <View className="flex-between">
            {order.goods.map((good) => (
              <Image key={good.goods_sn} src={good.goods_img} className="w-70 h-94 mr-4" mode="widthFix" />
            ))}
          </View>
        </ScrollView>
        {/* 右侧遮罩层 */}
        <View>
          <View>
            <View className="text-sm text-brand-898989 mb-2">Qty.{order.order_qty}</View>
            <View className="text-2xl font-bold text-brand-2D2E2C">¥{order.order_price}</View>
          </View>
        </View>

        {/* 订单底部信息 */}
        <View className="flex-between">
          {/* 检索 */}
          <View className="text-sm text-brand-2D2E2C leading-8">Customer: {order.custom}</View>
          <View className="bg-gray-900 text-white px-6 py-3 rounded-sm text-sm " onClick={() => handleRetrieve(order)}>
            Retrieve Order
          </View>
        </View>
      </View>
    );
  };

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: false,
        loadingFullScreen: false,
      }}
      headerType="withBack"
      showBottomPlaceholder>
      {/* 固定的搜索区域 */}
      <View className="sticky top-0 z-50 border-b border-gray-200 pt-8 pb-4 bg-white">
        <View className="flex items-center gap-3">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="flex items-center">
                <View className="w-20 h-20 bg-gray-100 border-1-area flex items-center justify-center rounded-sm  ml-3">
                  <IconFont name="event_available" color={"#666"} size={20} />
                </View>
                <View className="w-20 h-20 bg-gray-900 flex items-center justify-center rounded-sm  ml-3">
                  <IconFont name="scan" color={"#fff"} size={20} />
                </View>
              </View>
            }
          />
        </View>
      </View>

      {/* 订单列表区域 */}
      {orderList.length > 0 ? (
        <ScrollView className="flex-1 bg-gray-50 mx-8" scrollY enhanced showScrollbar={false} type="list" style={{ width: `calc(100% - 32px)` }}>
          {/* {orderList.map((order) => renderOrderItem(order))} */}
          {orderList.map((order) => (
            <OrderCard key={order.order_sn} {...order} class="w-full mb-6 " />
          ))}
        </ScrollView>
      ) : (
        <ErrorMessage msg={errorMessage} icon={errorIcon}></ErrorMessage>
      )}
    </MainLayout>
  );
};

export default observer(OrderHistoryPage);
