import { View, ScrollView, Image } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
// import { Image } from "@nutui/nutui-react-taro";

import { MainLayout, SearchInput, ErrorMessage } from "@/components";
import IconFont from "@/components/iconfont";
import http from "@/http";
import { useStores } from "@/hook";
import { toastStore } from "@/mobx";

// 订单数据类型定义 - 直接使用sit2接口字段名
interface OrderGoods {
  goods_sn: string;
  goods_name: string;
  goods_img: string;
  goods_price: number;
  discription: string;
  color: string;
  serial_no: string;
  size: string;
  barcode: string;
  status: string;
  detail: {
    MasterProductId: string;
    Material: string;
  };
  detail_img: string[];
  cm_url: string;
  alteration_url: string;
}

interface OrderItem {
  order_sn: string;
  order_time: string;
  order_status: string;
  order_price: number;
  order_qty: number;
  custom: string;
  goods: OrderGoods[];
}

const OrderHistoryPage = () => {
  const { loadingStore } = useStores();
  const [searchValue, setSearchValue] = useState("");
  const [orderList, setOrderList] = useState<OrderItem[]>([]);

  useEffect(() => {
    // 初始化加载订单数据
    loadOrderHistory();
  }, []);

  const loadOrderHistory = async () => {
    const loadingUUID = loadingStore.open({
      isFullScreen: true,
      showDelay: 0,
    });

    try {
      const response: any = await http.order.getOrderHistory({
        uid: "current_user_id", // 可以从store中获取当前用户ID
        page: 1,
        page_size: 20,
        search_input: searchValue || "",
        start_date: "",
        end_date: "",
      });

      if (response.code === 0) {
        // 直接使用sit2 API数据，不做字段转换
        setOrderList(response.data.list);
      } else {
        toastStore.show({
          content: response.msg || "加载订单历史失败",
          icon: "error",
        });
      }
    } catch (error) {
      toastStore.show({
        content: "加载订单历史失败",
        icon: "error",
      });
    } finally {
      loadingStore.close(loadingUUID);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    // TODO: 实现搜索逻辑
    console.log("search change:", value);
  };

  const handleRetrieve = async (order: OrderItem) => {
    try {
      const response: any = await http.order.retrieveOrder({
        order_id: order.order_sn,
      });

      if (response.code === 0) {
        Taro.showToast({
          title: "订单检索成功",
          icon: "success",
          duration: 2000,
        });
        // 可以在这里添加跳转到购物车的逻辑
        // Taro.navigateTo({ url: '/pages/cart/index' });
      } else {
        Taro.showToast({
          title: response.message || "检索失败",
          icon: "error",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("检索订单失败:", error);
      Taro.showToast({
        title: "检索失败，请重试",
        icon: "error",
        duration: 2000,
      });
    }
  };

  const renderOrderItem = (order: OrderItem) => {
    return (
      <View key={order.order_sn} className="bg-white border-1-area mb-6 py-10 px-6">
        {/* 订单头部信息 */}
        <View className="flex-between mb-2">
          <View className="text-sm leading-8 font-medium text-brand-2D2E2C mb-1">Orders: #{order.order_sn}</View>
          <View className="text-brand-898989 text-sm flex-center leading-8 font-medium">
            {order.order_status} <IconFont name="detail"></IconFont>
          </View>
        </View>
        <View className="flex-between mb-8">
          <View className="text-sm text-brand-898989 leading-8">{order.order_time}</View>
          <View className="text-sm text-brand-898989 leading-8">Customer: {order.custom}</View>
        </View>

        {/* 产品图片列表 */}
        <ScrollView className="w-full" scrollX enhanced showScrollbar={false} type="list">
          <View className="flex-between">
            {order.goods.map((good) => (
              <Image key={good.goods_sn} src={good.goods_img} className="w-70 h-94 mr-4" mode="widthFix" />
            ))}
          </View>
        </ScrollView>
        {/* 右侧遮罩层 */}
        <View>
          <View>
            <View className="text-sm text-brand-898989 mb-2">Qty.{order.order_qty}</View>
            <View className="text-2xl font-bold text-brand-2D2E2C">¥{order.order_price}</View>
          </View>
        </View>

        {/* 订单底部信息 */}
        <View className="flex justify-between items-end">
          <View className="bg-gray-900 text-white px-8 py-3 rounded-xl text-sm font-medium transition-colors" onClick={() => handleRetrieve(order)}>
            Retrieve Order
          </View>
        </View>
      </View>
    );
  };

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: false,
        loadingFullScreen: false,
      }}
      headerType="withBack"
      showBottomPlaceholder>
      {/* 固定的搜索区域 */}
      <View className="sticky top-0 z-50 border-b border-gray-200 pt-8 pb-4 bg-white">
        <View className="flex items-center gap-3">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="flex items-center">
                <View className="w-12 h-12 bg-gray-100 border-1-area flex items-center justify-center rounded-sm  ml-3">
                  <IconFont name="scan" color={"#666"} size={20} />
                </View>
                <View className="w-12 h-12 bg-gray-900 flex items-center justify-center rounded-sm  ml-3">
                  <IconFont name="scan" color={"#fff"} size={20} />
                </View>
              </View>
            }
          />
        </View>
      </View>

      {/* 订单列表区域 */}
      {orderList.length > 0 ? (
        <ScrollView className="flex-1 bg-gray-50 mx-8" scrollY enhanced showScrollbar={false} type="list" style={{ height: "calc(100vh - 140px)" }}>
          {orderList.map((order) => renderOrderItem(order))}
        </ScrollView>
      ) : (
        <ErrorMessage msg="No order history" icon="error"></ErrorMessage>
      )}
    </MainLayout>
  );
};

export default observer(OrderHistoryPage);
