import { View, ScrollView } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState } from "react";

import { MainLayout, SearchInput, ErrorMessage } from "@/components";
import IconFont from "@/components/iconfont";
import { useStores } from "@/hook";
import { OrderGoods } from "@/mobx/model/Order";

interface CategoryItem {
  id: string;
  name: string;
  icon?: string;
}

const CatalogPage = () => {
  const { loadingStore } = useStores();
  const [searchValue, setSearchValue] = useState("");
  const [goodsList, setGoodsList] = useState<OrderGoods[]>([]);
  const [searchError, setSearchError] = useState("");

  // 商品分类数据
  const categories: CategoryItem[] = [
    { id: "suits", name: "Suits" },
    { id: "jackets", name: "Jackets" },
    { id: "trousers", name: "Trousers" },
    { id: "coats", name: "Coats" },
    { id: "waistcoats", name: "Waistcoats / Vests" },
    { id: "knits", name: "Knits" },
    { id: "accessories", name: "Accessories" },
    { id: "shoes", name: "Shoes" },
  ];

  // 模拟商品搜索API
  const searchProducts = async (productId: string): Promise<OrderGoods[]> => {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟搜索结果
    const mockProducts: OrderGoods[] = [
      {
        goods_sn: "SS001",
        goods_name: "Classic Navy Suit",
        goods_img: "",
        goods_price: 899.00,
        discription: "Premium navy wool suit with classic fit",
        color: "Navy",
        serial_no: "SN001",
        size: "42R",
        barcode: "123456789001",
        status: "Available",
        detail: {
          MasterProductId: "MP001",
          Material: "Wool"
        },
        detail_img: [],
        cm_url: "",
        alteration_url: ""
      },
      {
        goods_sn: "SS002",
        goods_name: "Charcoal Wool Jacket",
        goods_img: "",
        goods_price: 599.00,
        discription: "Elegant charcoal wool jacket for business",
        color: "Charcoal",
        serial_no: "SN002",
        size: "40R",
        barcode: "123456789002",
        status: "Available",
        detail: {
          MasterProductId: "MP002",
          Material: "Wool"
        },
        detail_img: [],
        cm_url: "",
        alteration_url: ""
      }
    ];

    // 根据产品ID筛选
    const results = mockProducts.filter(product =>
      product.goods_sn.toLowerCase().includes(productId.toLowerCase()) ||
      product.goods_name.toLowerCase().includes(productId.toLowerCase())
    );

    return results;
  };

  const handleSearchChange = async (value: string) => {
    setSearchValue(value);
    setSearchError("");

    if (!value.trim()) {
      // 搜索框为空，显示catalog
      setGoodsList([]);
      return;
    }

    let loadingUuid: string | undefined;
    try {
      loadingUuid = loadingStore.open();
      const results = await searchProducts(value);

      if (results.length === 0) {
        setSearchError("No products found for your search");
        setGoodsList([]);
      } else {
        setGoodsList(results);
        setSearchError("");
      }
    } catch (error) {
      setSearchError("Search failed. Please try again.");
      setGoodsList([]);
    } finally {
      if (loadingUuid) {
        loadingStore.close(loadingUuid);
      }
    }
  };

  const handleCategoryClick = (category: CategoryItem) => {
    console.log("Selected category:", category);
    // 这里可以添加跳转到分类详情页的逻辑
  };



  return (
    <MainLayout className="h-screen flex flex-col bg-gray-50" showTopPlaceholder={false} headerType="withBack">
      {/* 固定的搜索区域 */}
      <View className="z-50 border-b border-gray-200 pt-8">
        <View className="flex items-center gap-3">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="w-20 h-20 bg-black flex-center rounded-sm ml-8">
                <IconFont name="scan" color={"#fff"} size={28} />
              </View>
            }
          />
        </View>
      </View>

      {/* 可滚动的分类列表 */}
      <ScrollView className="flex-1 bg-white px-8" style={{ width: `calc(100vw - 32px)` }} scrollY enhanced showScrollbar={false} type="list">
        {/* 根据搜索状态显示不同内容 */}
        {!searchValue.trim() ? (
          // 没有搜索内容时显示分类列表
          categories.map((category, index) => (
            <View key={category.id} className={`border-b-1 h-36 flex items-center justify-between ${index !== categories.length - 1 ? "border-b" : ""}`} onClick={() => handleCategoryClick(category)}>
              <View className="text-lg font-normal text-brand-2D2E2C leading-6">{category.name}</View>
              <View className="text-brand-999999">
                <IconFont name="detail" color={"#999999"} size={20} />
              </View>
            </View>
          ))
        ) : searchError ? (
          // 搜索出错或没有结果时显示错误信息
          <ErrorMessage
            msg={searchError}
            icon="error"
          />
        ) : goodsList.length > 0 ? (
          // 有搜索结果时显示商品列表
          goodsList.map((product, index) => (
            <View key={product.goods_sn} className={`border-b-1 h-36 flex items-center justify-between ${index !== goodsList.length - 1 ? "border-b" : ""}`}>
              <View className="flex-1">
                <View className="text-lg font-normal text-brand-2D2E2C leading-6">{product.goods_name}</View>
                <View className="text-sm text-brand-999999 mt-1">
                  {product.goods_sn} • {product.color} • {product.size}
                </View>
                <View className="text-sm text-brand-2D2E2C mt-1 font-semibold">
                  ${product.goods_price.toFixed(2)}
                </View>
              </View>
              <View className="text-brand-999999">
                <IconFont name="detail" color={"#999999"} size={20} />
              </View>
            </View>
          ))
        ) : loadingStore.isShow ? (
          // 搜索中显示加载状态
          <View className="flex items-center justify-center py-20">
            <View className="text-brand-999999">Searching...</View>
          </View>
        ) : null}
      </ScrollView>
    </MainLayout>
  );
};

export default observer(CatalogPage);
