import { View, ScrollView } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState } from "react";

import { MainLayout, SearchInput } from "@/components";
import IconFont from "@/components/iconfont";

interface CategoryItem {
  id: string;
  name: string;
  icon?: string;
}

const CatalogPage = () => {
  const [searchValue, setSearchValue] = useState("");

  // 商品分类数据
  const categories: CategoryItem[] = [
    { id: "suits", name: "Suits" },
    { id: "jackets", name: "Jackets" },
    { id: "trousers", name: "Trousers" },
    { id: "coats", name: "Coats" },
    { id: "waistcoats", name: "Waistcoats / Vests" },
    { id: "knits", name: "Knits" },
    { id: "accessories", name: "Accessories" },
    { id: "shoes", name: "Shoes" },
  ];

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    // 这里可以添加搜索逻辑
    console.log("Search:", value);
  };

  const handleCategoryClick = (category: CategoryItem) => {
    console.log("Selected category:", category);
    // 这里可以添加跳转到分类详情页的逻辑
  };

  const handleScanClick = () => {
    console.log("Scan button clicked");
    // 这里可以添加扫码逻辑
  };

  return (
    <MainLayout className="h-screen flex flex-col bg-gray-50" showTopPlaceholder={false} headerType="withBack">
      {/* 固定的搜索区域 */}
      <View className="z-50 border-b border-gray-200 pt-8">
        <View className="flex items-center gap-3">
          <SearchInput
            placeholder="Search Product ID"
            onChange={handleSearchChange}
            clearable
            richtIcon={
              <View className="w-20 h-20 bg-black flex-center rounded-sm ml-8">
                <IconFont name="scan" color={"#fff"} size={28} />
              </View>
            }
          />
        </View>
      </View>

      {/* 可滚动的分类列表 */}
      <ScrollView className="flex-1 bg-white px-8" style={{ width: `calc(100vw - 32px)` }} scrollY enhanced showScrollbar={false} type="list">
        {categories.map((category, index) => (
          <View key={category.id} className={`border-b-1 h-36 flex items-center justify-between	${index !== categories.length - 1 ? "border-b" : ""}`} onClick={() => handleCategoryClick(category)}>
            <View className="text-lg leading-12 font-normal text-brand-2D2E2C leading-6">{category.name}</View>
            <View className="text-brand-999999">
              <IconFont name="detail" color={"#999999"} size={20} />
            </View>
          </View>
        ))}
      </ScrollView>
    </MainLayout>
  );
};

export default observer(CatalogPage);
