import Taro from "@tarojs/taro";
import { WebView } from "@tarojs/components";
import { useEffect, useState } from "react";
import { observer } from "mobx-react";
import { useStores } from "@/hook";
import { backPage } from "@/utils";
import { toastStore } from "@/mobx";

const CustomMadeWebView = () => {
  const { userStore } = useStores();
  const [webviewUrl, setWebviewUrl] = useState("");

  useEffect(() => {
    // 获取路由参数
    const instance = Taro.getCurrentInstance();
    const params = instance.router?.params || {};

    // 构建 SuitSupply 定制页面 URL
    const baseUrl = "https://testing-v4.custom-made.suitsupply.com/";
    const urlParams = new URLSearchParams();

    // https://suitsupply.com/en-us/custom-made?productCode=C6801&productCode=B3019&client=onLine&product=Suit&countryCode=US&fabricCode=IRIDESSA%2F1261&section=jacket&level=group
    // 添加基础参数
    if (params.productCode) {
      // 支持多个 productCode
      const productCodes = Array.isArray(params.productCode) ? params.productCode : [params.productCode];
      productCodes.forEach((code) => urlParams.append("productCode", code));
    }

    if (params.client) urlParams.append("client", params.client);
    if (params.product) urlParams.append("product", params.product);
    if (params.countryCode) urlParams.append("countryCode", params.countryCode);
    if (params.fabricCode) urlParams.append("fabricCode", params.fabricCode);
    if (params.section) urlParams.append("section", params.section);
    if (params.level) urlParams.append("level", params.level);

    // 添加用户信息（如果已登录）
    if (userStore.isLogin && userStore.userInfo) {
      // 可以添加用户相关参数
      if (userStore.userInfo.username) {
        urlParams.append("user", userStore.userInfo.username);
      }
    }

    const finalUrl = urlParams.toString() ? `${baseUrl}?${urlParams.toString()}` : baseUrl;
    setWebviewUrl(finalUrl);

    console.log("Custom Made WebView URL:", finalUrl);
  }, [userStore.isLogin, userStore.userInfo]);

  // Handle messages from web - view - only handle the completion signal
  const handleMessage = (e: any) => {
    console.log("Received custom page message:", e.detail.data);

    const data = e.detail.data[0];
    if (!data) return;

    // 只处理完成信号，其余逻辑由 HTML 页面自己完成
    if (data.type === "CUSTOM_COMPLETED") {
      // Add to Cart and jump to cart page
      handleCustomCompleted(data.payload);
    } else {
      console.log("Received other messages, which are handled by the HTML page itself:", data.type);
    }
  };

  // 处理定制完成信号
  const handleCustomCompleted = async (payload: any) => {
    try {
      toastStore.show("定制完成");
      // 可以在这里调用后端 API 保存最终结果
      // await customStore.saveCustomResult(payload);

      // 延迟返回上一页或跳转到指定页面
      setTimeout(() => {
        if (payload.redirectTo) {
          Taro.navigateTo({ url: payload.redirectTo });
        } else {
          Taro.navigateBack();
        }
      }, 2000);
    } catch (error) {
      console.error("处理定制完成信号失败:", error);
      toastStore.show("处理失败");
    }
  };

  if (!webviewUrl) {
    return null;
  }

  return (
    <WebView
      src={webviewUrl} // custom-made page URL
      onMessage={handleMessage}
      onLoad={() => {
        console.log("The custom-made page has finished loading");
      }}
      onError={(e) => {
        backPage();
      }}
    />
  );
};

export default observer(CustomMadeWebView);
