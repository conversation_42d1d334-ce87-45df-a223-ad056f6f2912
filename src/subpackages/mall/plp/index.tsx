import { View } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import { Image } from "@nutui/nutui-react-taro";

import { MainLayout } from "@/components";
import { useI18n, useStores } from "@/hook";
import IconFont, { IconNames } from "@/components/iconfont";
const HomePage = () => {
  const { t } = useI18n();

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      // 使用便捷配置方式
      headerType="withBack"
      showBottomPlaceholder>
    </MainLayout>
  );
};

export default observer(HomePage);
