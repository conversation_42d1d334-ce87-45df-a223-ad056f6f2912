import { getBaseURL } from "../baseURL";
import { createApis } from '../utils/apiConfig'

// 获取客户模块的baseURL（可能是mock或真实接口）
const customerBaseURL = getBaseURL('customer');

// 小程序环境下，直接使用完整 URL
// 根据配置自动选择mock或真实接口
const customer = createApis({
  regist: {
    url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_register`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
	searchCustomers:{
		 url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_list_query`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},
	  queryCustomerInfo:{
		 url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_detail_query`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},

  updateMemberInfo:{
		 url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_update`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},
})

export default customer;
