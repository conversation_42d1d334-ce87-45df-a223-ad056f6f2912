import { baseURL } from "../baseURL";
import { createApis } from '../utils/apiConfig'

// 小程序环境下，直接使用完整 URL
// Taro Mock 插件会在启用 --mock 参数时自动拦截请求
const customer = createApis({
  regist: {
    url: `${baseURL}/web/mobile/default.php?c=customer&m=member_register`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
	searchCustomers:{
		 url: `${baseURL}/web/mobile/default.php?c=customer&m=member_list_query`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},
	  queryCustomerInfo:{
		 url: `${baseURL}/web/mobile/default.php?c=customer&m=member_detail_query`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},

  updateMemberInfo:{
		 url: `${baseURL}/web/mobile/default.php?c=customer&m=member_update`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},
})

export default customer;
