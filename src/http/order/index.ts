import { getBaseURL } from "../baseURL";
import { createApis } from '../utils/apiConfig'

// 获取订单模块的baseURL（可能是mock或真实接口）
const orderBaseURL = getBaseURL('order');

// 小程序环境下，直接使用完整 URL
// 根据配置自动选择mock或真实接口
const order = createApis({
  // 获取订单历史列表
  getOrderHistory: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=history_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 搜索订单
  searchOrders: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=search`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "搜索中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 获取订单详情
  getOrderDetail: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=detail`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 检索订单（Retrieve）
  retrieveOrder: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=retrieve`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "检索中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
})

export default order;
