import { baseURL } from "../baseURL";
import { createApis } from '../utils/apiConfig'

// 小程序环境下，直接使用完整 URL
// Taro Mock 插件会在启用 --mock 参数时自动拦截请求
const order = createApis({
  // 获取订单历史列表
  getOrderHistory: {
    url: `${baseURL}/web/mobile/default.php?c=order&m=history_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
  
  // 搜索订单
  searchOrders: {
    url: `${baseURL}/web/mobile/default.php?c=order&m=search`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "搜索中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
  
  // 获取订单详情
  getOrderDetail: {
    url: `${baseURL}/web/mobile/default.php?c=order&m=detail`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
  
  // 检索订单（Retrieve）
  retrieveOrder: {
    url: `${baseURL}/web/mobile/default.php?c=order&m=retrieve`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "检索中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
})

export default order;
