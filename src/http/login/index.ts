import { baseURL } from "../baseURL";
import { createApis } from "../utils/apiConfig";

// 小程序环境下，直接使用完整 URL
// Taro Mock 插件会在启用 --mock 参数时自动拦截请求
const login = createApis({
  // 普通登录 - 重要操作，使用全屏loading
  login: {
    url: `${baseURL}/web/mobile/auth.php`,
    preset: "IMPORTANT_ACTION",
    AuthorizationType: "auth",
    overrides: {
      loadingText: "登录中...",
    },
  },

  loginByPhone: {
    url: `${baseURL}/web/mobile/phone-auth.php`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "登录中...",
    },
  },

  // 微软登录 - 验证微软访问令牌并获取用户信息
  validateMicrosoftToken: {
    url: `${baseURL}/web/mobile/microsoft-auth.php`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "验证中...",
    },
  },
});

// 也可以继续使用传统方式创建特殊配置的接口
// const customLogin = creator({
//   url: `${baseURL}/web/mobile/custom-auth.php`,
//   method: 'POST',
//   Authorization: false,
//   loading: true,
//   loadingFullScreen: true,
//   showDelay: 0,
//   loadingText: '自定义登录中...',
//   contentType: 'application/x-www-form-urlencoded'
// })

export default login;
