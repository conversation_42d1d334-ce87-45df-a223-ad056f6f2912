import { baseURL } from "../baseURL";
import { createApis } from "../utils/apiConfig";

// 小程序环境下，直接使用完整 URL
// Taro Mock 插件会在启用 --mock 参数时自动拦截请求
const common = createApis({
  default: {
    url: `${baseURL}/web/mobile/default.php`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  getDianyuanDetail: {
    url: `${baseURL}/web/mobile/default.php?c=user&m=user_info`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  getPaymentList: {
    url: `${baseURL}/web/mobile/default.php?c=payment_terminal&m=list`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  getStoreList: {
    url: `${baseURL}/web/mobile/default.php?c=store&m=list`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },

  getSale: {
    url: `${baseURL}/web/mobile/default.php?c=user&m=home_yj`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  onChangeDeviceInfo: {
    url: `${baseURL}/web/mobile/default.php?c=user&m=setting_save`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  onLogOut: {
    url: `${baseURL}/web/mobile/default.php?c=user&m=logout`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },

});

export default common;
