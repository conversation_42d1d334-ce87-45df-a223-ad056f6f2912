import { observer } from 'mobx-react'
import './index.scss'
import { View } from '@tarojs/components'
import IconFont from '../iconfont'

const EmptyBox: React.FC<any> = (props) => {
  return (
    <View className='empty_box_content'>
      <IconFont
        style={{ display: 'inline-block' }}
        name='shopping_bag'
        size={67}
        color='#999'
      />
      <View className='empty'>您还未选购相关商品，请先选购</View>
    </View>
  )
}
export default observer(EmptyBox)
