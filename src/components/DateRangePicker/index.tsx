import React, { useState, useEffect } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { Popup, Calendar, ConfigProvider } from '@nutui/nutui-react-taro';
import enUS from '@nutui/nutui-react-taro/dist/locales/en-US';

interface DateRangePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (dateRange: { start_date: string; end_date: string }) => void;
  initialStartDate?: string;
  initialEndDate?: string;
  title?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  initialStartDate = '',
  initialEndDate = '',
  title = 'Select time'
}) => {
  const [selectedRange, setSelectedRange] = useState<string[]>([]);

  // 当组件接收到新的初始值时更新状态
  useEffect(() => {
    const range: string[] = [];
    if (initialStartDate) range.push(initialStartDate);
    if (initialEndDate) range.push(initialEndDate);
    setSelectedRange(range);
  }, [initialStartDate, initialEndDate]);





  // 确认选择
  const handleConfirm = () => {
    if (selectedRange.length < 2) {
      return;
    }

    const [start_date, end_date] = selectedRange.sort();
    onConfirm({ start_date, end_date });
    onClose();
  };

  // 重置选择
  const handleReset = () => {
    setSelectedRange([]);
  };

  // 处理日历选择
  const handleCalendarConfirm = (param: any) => {
    if (Array.isArray(param)) {
      setSelectedRange(param);
    } else if (typeof param === 'string') {
      // 如果是单个日期字符串，需要处理为数组
      setSelectedRange([param]);
    }
  };

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      round
      closeable
      closeIconPosition="top-right"
      style={{
        height: 'auto',
        '--nutui-calendar-primary-color': '#2D2E2C',
        '--nutui-calendar-choose-color': '#2D2E2C',
        '--nutui-calendar-range-color': '#F6F6F6',
        '--nutui-calendar-base-color': '#2D2E2C',
        '--nutui-calendar-active-color': '#2D2E2C'
      } as any}
    >
      <View className="p-6">
        {/* 标题 */}
        <View className="text-center mb-6">
          <Text className="text-lg font-semibold" style={{ color: '#2D2E2C' }}>{title}</Text>
        </View>

        {/* 日历组件 */}
        <ConfigProvider locale={enUS}>
          <View style={{
            '--nutui-calendar-primary-color': '#2D2E2C',
            '--nutui-calendar-choose-color': '#2D2E2C',
            '--nutui-calendar-range-color': '#F6F6F6',
            '--nutui-calendar-base-color': '#2D2E2C'
          } as any}>
            <Calendar
              type="range"
              visible={true}
              defaultValue={selectedRange}
              onConfirm={handleCalendarConfirm}
              startText="Start"
              endText="End"
              confirmText="Confirm"
              title=""
            />
          </View>
        </ConfigProvider>

        {/* 按钮区域 */}
        <View className="flex space-x-3 mt-6">
          <Button
            className="flex-1 border-0 rounded-lg"
            style={{ backgroundColor: '#F6F6F6', color: '#2D2E2C' }}
            onClick={handleReset}
          >
            Reset
          </Button>
          <Button
            className="flex-1 border-0 rounded-lg text-white"
            style={{ backgroundColor: '#2D2E2C' }}
            onClick={handleConfirm}
          >
            Confirm
          </Button>
        </View>
      </View>
    </Popup>
  );
};

export default DateRangePicker;
