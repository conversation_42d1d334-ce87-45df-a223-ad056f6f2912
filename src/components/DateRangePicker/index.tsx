import React, { useState, useEffect } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { Popup, DatePicker } from '@nutui/nutui-react-taro';

interface DateRangePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (dateRange: { start_date: string; end_date: string }) => void;
  initialStartDate?: string;
  initialEndDate?: string;
  title?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  initialStartDate = '',
  initialEndDate = '',
  title = '选择日期范围'
}) => {
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);

  // 当组件接收到新的初始值时更新状态
  useEffect(() => {
    setStartDate(initialStartDate);
    setEndDate(initialEndDate);
  }, [initialStartDate, initialEndDate]);

  // 解析日期字符串为 Date 对象
  const parseDate = (dateStr: string): Date => {
    if (!dateStr) return new Date();
    const [year, month, day] = dateStr.split('-').map(Number);
    return new Date(year, month - 1, day);
  };

  // 处理开始日期选择
  const handleStartDateConfirm = (_: any, selectedValue: (string | number)[]) => {
    // NutUI DatePicker 返回的是 [year, month, day] 数组
    const [year, month, day] = selectedValue;
    const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    setStartDate(formattedDate);
    setShowStartPicker(false);

    // 如果开始日期晚于结束日期，清空结束日期
    if (endDate && formattedDate > endDate) {
      setEndDate('');
    }
  };

  // 处理结束日期选择
  const handleEndDateConfirm = (_: any, selectedValue: (string | number)[]) => {
    // NutUI DatePicker 返回的是 [year, month, day] 数组
    const [year, month, day] = selectedValue;
    const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    setEndDate(formattedDate);
    setShowEndPicker(false);
  };

  // 确认选择
  const handleConfirm = () => {
    if (!startDate || !endDate) {
      // 可以在这里添加提示
      return;
    }

    if (startDate > endDate) {
      // 可以在这里添加错误提示
      return;
    }

    onConfirm({
      start_date: startDate,
      end_date: endDate
    });
    onClose();
  };

  // 重置选择
  const handleReset = () => {
    setStartDate('');
    setEndDate('');
  };

  // 获取显示文本
  const getDisplayText = (date: string, placeholder: string) => {
    return date || placeholder;
  };

  return (
    <>
      <Popup
        visible={visible}
        position="bottom"
        onClose={onClose}
        round
        closeable
        closeIconPosition="top-right"
        style={{ height: 'auto' }}
      >
        <View className="p-6">
          {/* 标题 */}
          <View className="text-center mb-6">
            <Text className="text-lg font-semibold text-gray-800">{title}</Text>
          </View>

          {/* 日期选择区域 */}
          <View className="space-y-4">
            {/* 开始日期 */}
            <View>
              <Text className="text-sm text-gray-600 mb-2">开始日期</Text>
              <View
                className="border border-gray-300 rounded-lg p-3 bg-white"
                onClick={() => setShowStartPicker(true)}
              >
                <Text className={startDate ? "text-gray-800" : "text-gray-400"}>
                  {getDisplayText(startDate, "请选择开始日期")}
                </Text>
              </View>
            </View>

            {/* 结束日期 */}
            <View>
              <Text className="text-sm text-gray-600 mb-2">结束日期</Text>
              <View
                className="border border-gray-300 rounded-lg p-3 bg-white"
                onClick={() => setShowEndPicker(true)}
              >
                <Text className={endDate ? "text-gray-800" : "text-gray-400"}>
                  {getDisplayText(endDate, "请选择结束日期")}
                </Text>
              </View>
            </View>
          </View>

          {/* 按钮区域 */}
          <View className="flex space-x-3 mt-6">
            <Button
              className="flex-1 bg-gray-200 text-gray-700 border-0 rounded-lg"
              onClick={handleReset}
            >
              重置
            </Button>
            <Button
              className="flex-1 bg-blue-500 text-white border-0 rounded-lg"
              onClick={handleConfirm}
            >
              确定
            </Button>
          </View>
        </View>
      </Popup>

      {/* 开始日期选择器 */}
      <DatePicker
        visible={showStartPicker}
        type="date"
        title="选择开始日期"
        defaultValue={startDate ? parseDate(startDate) : new Date()}
        onClose={() => setShowStartPicker(false)}
        onConfirm={handleStartDateConfirm}
      />

      {/* 结束日期选择器 */}
      <DatePicker
        visible={showEndPicker}
        type="date"
        title="选择结束日期"
        defaultValue={endDate ? parseDate(endDate) : new Date()}
        onClose={() => setShowEndPicker(false)}
        onConfirm={handleEndDateConfirm}
      />
    </>
  );
};

export default DateRangePicker;
