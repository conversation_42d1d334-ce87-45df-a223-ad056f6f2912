import React, { useState } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { Popup, Calendar, ConfigProvider } from '@nutui/nutui-react-taro';
import enUS from '@nutui/nutui-react-taro/dist/locales/en-US';

interface DateRangePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (dateRange: { start_date: string; end_date: string }) => void;
  initialStartDate?: string;
  initialEndDate?: string;
  title?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  initialStartDate = '',
  initialEndDate = '',
  title = 'Select time'
}) => {
  const [selectedRange, setSelectedRange] = useState<string[]>([]);

  // 确认选择
  const handleConfirm = () => {
    if (selectedRange.length < 2) {
      return;
    }

    // 确保日期格式为 YYYY-MM-DD 并排序
    const sortedDates = selectedRange.sort();
    const start_date = sortedDates[0]; // 开始时间
    const end_date = sortedDates[1];   // 结束时间

    console.log('Confirming date range:', { start_date, end_date });
    onConfirm({ start_date, end_date });
    onClose();
  };

  // 重置选择
  const handleReset = () => {
    setSelectedRange([]);
  };

  // 格式化日期为 YYYY-MM-DD 格式
  const formatDate = (date: any): string => {
    if (typeof date === 'string') {
      // 如果已经是字符串格式，检查是否符合 YYYY-MM-DD
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return date;
      }
    }

    // 如果是Date对象或其他格式，转换为 YYYY-MM-DD
    const dateObj = new Date(date);
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 处理日历选择 - 确保返回正确的日期格式
  const handleCalendarConfirm = (param: any) => {
    console.log('Calendar confirm param:', param);

    if (Array.isArray(param)) {
      // 格式化数组中的每个日期
      const formattedDates = param.map(date => formatDate(date));
      setSelectedRange(formattedDates);
    } else if (param) {
      // 格式化单个日期
      const formattedDate = formatDate(param);
      setSelectedRange([formattedDate]);
    }
  };

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      round
      closeable
      closeIconPosition="top-right"
      style={{ height: 'auto' }}
    >
      <View className="p-6">
        {/* 标题 */}
        <View className="text-center mb-6">
          <Text className="text-lg font-semibold" style={{ color: '#2D2E2C' }}>{title}</Text>
        </View>

        {/* 显示当前选择 */}
        {selectedRange.length > 0 && (
          <View className="mb-4 p-3 rounded-lg" style={{ backgroundColor: '#F6F6F6' }}>
            <Text className="text-sm" style={{ color: '#2D2E2C' }}>
              {selectedRange.length === 1
                ? `Start: ${selectedRange[0]}`
                : `${selectedRange[0]} to ${selectedRange[1]}`
              }
            </Text>
          </View>
        )}

        {/* 日历组件 - 移除defaultValue避免格式问题 */}
        <ConfigProvider locale={enUS}>
          <View style={{
            '--nutui-calendar-primary-color': '#2D2E2C',
            '--nutui-calendar-choose-color': '#2D2E2C',
            '--nutui-calendar-range-color': '#F6F6F6',
            '--nutui-calendar-base-color': '#2D2E2C'
          } as any}>
            <Calendar
              type="range"
              visible={true}
              onConfirm={handleCalendarConfirm}
              startText="Start"
              endText="End"
              confirmText="Confirm"
              title=""
            />
          </View>
        </ConfigProvider>

        {/* 按钮区域 */}
        <View className="flex space-x-3 mt-6">
          <Button
            className="flex-1 border-0 rounded-lg"
            style={{ backgroundColor: '#F6F6F6', color: '#2D2E2C' }}
            onClick={handleReset}
          >
            Reset
          </Button>
          <Button
            className="flex-1 border-0 rounded-lg text-white"
            style={{ backgroundColor: '#2D2E2C' }}
            onClick={handleConfirm}
          >
            Confirm
          </Button>
        </View>
      </View>
    </Popup>
  );
};

export default DateRangePicker;