import React, { useState } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { Popup, Calendar, ConfigProvider } from '@nutui/nutui-react-taro';
import enUS from '@nutui/nutui-react-taro/dist/locales/en-US';

interface DateRangePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (dateRange: { start_date: string; end_date: string }) => void;
  initialStartDate?: string;
  initialEndDate?: string;
  title?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  initialStartDate = '',
  initialEndDate = '',
  title = 'Select time'
}) => {
  const [selectedRange, setSelectedRange] = useState<string[]>([]);

  // 确认选择
  const handleConfirm = () => {
    if (selectedRange.length < 2) {
      return;
    }

    const [start_date, end_date] = selectedRange.sort();
    onConfirm({ start_date, end_date });
    onClose();
  };

  // 重置选择
  const handleReset = () => {
    setSelectedRange([]);
  };

  // 处理日历选择 - 完全不使用defaultValue，避免格式问题
  const handleCalendarConfirm = (param: any) => {
    console.log('Calendar confirm param:', param);
    if (Array.isArray(param)) {
      setSelectedRange(param);
    } else if (typeof param === 'string') {
      setSelectedRange([param]);
    }
  };

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      round
      closeable
      closeIconPosition="top-right"
      style={{ height: 'auto' }}
    >
      <View className="p-6">
        {/* 标题 */}
        <View className="text-center mb-6">
          <Text className="text-lg font-semibold" style={{ color: '#2D2E2C' }}>{title}</Text>
        </View>

        {/* 显示当前选择 */}
        {selectedRange.length > 0 && (
          <View className="mb-4 p-3 bg-gray-100 rounded-lg">
            <Text className="text-sm text-gray-600">
              Selected: {selectedRange.join(' to ')}
            </Text>
          </View>
        )}

        {/* 日历组件 - 移除defaultValue避免格式问题 */}
        <ConfigProvider locale={enUS}>
          <View style={{
            '--nutui-calendar-primary-color': '#2D2E2C',
            '--nutui-calendar-choose-color': '#2D2E2C',
            '--nutui-calendar-range-color': '#F6F6F6',
            '--nutui-calendar-base-color': '#2D2E2C'
          } as any}>
            <Calendar
              type="range"
              visible={true}
              onConfirm={handleCalendarConfirm}
              startText="Start"
              endText="End"
              confirmText="Confirm"
              title=""
            />
          </View>
        </ConfigProvider>

        {/* 按钮区域 */}
        <View className="flex space-x-3 mt-6">
          <Button
            className="flex-1 border-0 rounded-lg"
            style={{ backgroundColor: '#F6F6F6', color: '#2D2E2C' }}
            onClick={handleReset}
          >
            Reset
          </Button>
          <Button
            className="flex-1 border-0 rounded-lg text-white"
            style={{ backgroundColor: '#2D2E2C' }}
            onClick={handleConfirm}
          >
            Confirm
          </Button>
        </View>
      </View>
    </Popup>
  );
};

export default DateRangePicker;