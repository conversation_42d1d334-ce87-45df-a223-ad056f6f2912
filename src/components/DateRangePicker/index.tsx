import React, { useState } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { Popup, Calendar, ConfigProvider } from '@nutui/nutui-react-taro';
import enUS from '@nutui/nutui-react-taro/dist/locales/en-US';

interface DateRangePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (dateRange: { start_date: string; end_date: string }) => void;
  initialStartDate?: string;
  initialEndDate?: string;
  title?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  initialStartDate = '',
  initialEndDate = '',
  title = 'Select time'
}) => {
  const [selectedRange, setSelectedRange] = useState<string[]>([]);



  // 重置选择
  const handleReset = () => {
    setSelectedRange([]);
  };

  // 格式化日期为 YYYY-MM-DD 格式
  const formatDate = (date: any): string => {
    if (typeof date === 'string') {
      // 如果已经是字符串格式，检查是否符合 YYYY-MM-DD
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return date;
      }
    }

    // 如果是数组格式 [year, month, day]，直接转换
    if (Array.isArray(date) && date.length >= 3) {
      const [year, month, day] = date;
      return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    }

    // 如果是Date对象或其他格式，转换为 YYYY-MM-DD
    const dateObj = new Date(date);
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 处理日历选择 - 点击选择后立即关闭并传参
  const handleCalendarConfirm = (param: any) => {
    console.log('Calendar confirm param:', param);

    let formattedDates: string[] = [];

    // 根据控制台输出，Calendar返回的是二维数组格式
    // param 是 [Array(5), Array(5)] 格式
    if (Array.isArray(param)) {
      // 遍历每个日期数组，提取日期信息
      formattedDates = param.map(dateArray => {
        if (Array.isArray(dateArray) && dateArray.length >= 3) {
          // dateArray 格式: ["2025", "07", "30", "2025/07/30", "星期三"]
          // 或者: [2025, "08", "02", "2025/08/02", "星期六"]
          const [year, month, day] = dateArray;
          return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        }
        return formatDate(dateArray);
      });
    } else if (param) {
      // 格式化单个日期
      formattedDates = [formatDate(param)];
    }

    console.log('Formatted dates:', formattedDates);

    // 如果选择了完整的日期范围（2个日期），立即关闭并传参
    if (formattedDates.length >= 2) {
      const sortedDates = formattedDates.sort();
      const start_date = sortedDates[0]; // 开始时间
      const end_date = sortedDates[1];   // 结束时间

      console.log('Auto confirming date range:', { start_date, end_date });
      onConfirm({ start_date, end_date });
      onClose(); // 立即关闭弹窗
    } else {
      // 如果只选择了一个日期，更新状态但不关闭
      setSelectedRange(formattedDates);
    }
  };

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      round
      closeable
      closeIconPosition="top-right"
      style={{ height: 'auto' }}
    >
      <View className="p-6">
        {/* 标题 */}
        <View className="text-center mb-6">
          <Text className="text-lg font-semibold" style={{ color: '#2D2E2C' }}>{title}</Text>
        </View>

        {/* 显示当前选择 */}
        {selectedRange.length > 0 && (
          <View className="mb-4 p-3 rounded-lg" style={{ backgroundColor: '#F6F6F6' }}>
            <Text className="text-sm" style={{ color: '#2D2E2C' }}>
              {selectedRange.length === 1
                ? `Start: ${selectedRange[0]}`
                : `${selectedRange[0]} to ${selectedRange[1]}`
              }
            </Text>
          </View>
        )}

        {/* 日历组件 - 移除defaultValue避免格式问题 */}
        <ConfigProvider locale={enUS}>
          <View style={{
            '--nutui-calendar-primary-color': '#2D2E2C',
            '--nutui-calendar-choose-color': '#2D2E2C',
            '--nutui-calendar-range-color': '#F6F6F6',
            '--nutui-calendar-base-color': '#2D2E2C'
          } as any}>
            <Calendar
              type="range"
              visible={true}
              onConfirm={handleCalendarConfirm}
              startText="Start"
              endText="End"
              confirmText="Confirm"
              title=""
            />
          </View>
        </ConfigProvider>

        {/* 按钮区域 - 只保留重置按钮，选择完成后自动确认 */}
        <View className="flex justify-center mt-6">
          <Button
            className="flex-1 border-0 rounded-lg"
            style={{ backgroundColor: '#F6F6F6', color: '#2D2E2C' }}
            onClick={handleReset}
          >
            Reset
          </Button>
        </View>
      </View>
    </Popup>
  );
};

export default DateRangePicker;