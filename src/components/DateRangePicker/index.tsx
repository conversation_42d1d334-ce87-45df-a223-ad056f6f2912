import React, { useState, useEffect } from 'react';
import { View, Text, Button } from '@tarojs/components';
import { Popup, Calendar, ConfigProvider } from '@nutui/nutui-react-taro';
import enUS from '@nutui/nutui-react-taro/dist/locales/en-US';

interface DateRangePickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (dateRange: { start_date: string; end_date: string }) => void;
  initialStartDate?: string;
  initialEndDate?: string;
  title?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  visible,
  onClose,
  onConfirm,
  initialStartDate = '',
  initialEndDate = '',
  title = 'Select time'
}) => {
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);

  // 当组件接收到新的初始值时更新状态
  useEffect(() => {
    setStartDate(initialStartDate);
    setEndDate(initialEndDate);
  }, [initialStartDate, initialEndDate]);

  // 格式化日期为 YYYY-MM-DD 格式
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 处理日历选择 - 单个日期点击
  const handleDayClick = (date: string) => {
    if (!startDate || (startDate && endDate)) {
      // 如果没有开始日期，或者已经选择了完整范围，重新开始选择
      setStartDate(date);
      setEndDate('');
    } else if (startDate && !endDate) {
      // 如果有开始日期但没有结束日期，设置结束日期
      if (date >= startDate) {
        setEndDate(date);
      } else {
        // 如果选择的日期早于开始日期，重新设置开始日期
        setStartDate(date);
        setEndDate('');
      }
    }
  };

  // 处理日历确认
  const handleCalendarConfirm = (selectedDate: string) => {
    // Calendar的onConfirm返回单个日期字符串
    if (!startDate) {
      setStartDate(selectedDate);
    } else if (!endDate) {
      if (selectedDate >= startDate) {
        setEndDate(selectedDate);
      } else {
        setStartDate(selectedDate);
        setEndDate('');
      }
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (!startDate || !endDate) {
      // 可以在这里添加提示
      return;
    }

    if (startDate > endDate) {
      // 可以在这里添加错误提示
      return;
    }

    onConfirm({
      start_date: startDate,
      end_date: endDate
    });
    onClose();
  };

  // 重置选择
  const handleReset = () => {
    setStartDate('');
    setEndDate('');
  };

  // 获取当前选择的日期数组
  const getSelectedDates = () => {
    const dates: string[] = [];
    if (startDate) dates.push(startDate);
    if (endDate && endDate !== startDate) dates.push(endDate);
    return dates;
  };

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      round
      closeable
      closeIconPosition="top-right"
      style={{
        height: 'auto',
        '--nutui-calendar-primary-color': '#2D2E2C',
        '--nutui-calendar-choose-color': '#2D2E2C',
        '--nutui-calendar-range-color': '#F6F6F6',
        '--nutui-calendar-base-color': '#2D2E2C',
        '--nutui-calendar-active-color': '#2D2E2C'
      } as any}
    >
      <View className="p-6">
        {/* 标题 */}
        <View className="text-center mb-6">
          <Text className="text-lg font-semibold" style={{ color: '#2D2E2C' }}>{title}</Text>
        </View>

        {/* 日历组件 */}
        <ConfigProvider locale={enUS}>
          <View style={{
            '--nutui-calendar-primary-color': '#2D2E2C',
            '--nutui-calendar-choose-color': '#2D2E2C',
            '--nutui-calendar-range-color': '#F6F6F6',
            '--nutui-calendar-base-color': '#2D2E2C'
          } as any}>
            <Calendar
              type="range"
              visible={true}
              defaultValue={getSelectedDates()}
              onDayClick={handleDayClick}
              onConfirm={handleCalendarConfirm}
              startText="Start"
              endText="End"
              confirmText="Confirm"
              title=""
            />
          </View>
        </ConfigProvider>

        {/* 按钮区域 */}
        <View className="flex space-x-3 mt-6">
          <Button
            className="flex-1 border-0 rounded-lg"
            style={{ backgroundColor: '#F6F6F6', color: '#2D2E2C' }}
            onClick={handleReset}
          >
            Reset
          </Button>
          <Button
            className="flex-1 border-0 rounded-lg text-white"
            style={{ backgroundColor: '#2D2E2C' }}
            onClick={handleConfirm}
          >
            Confirm
          </Button>
        </View>
      </View>
    </Popup>
  );
};

export default DateRangePicker;
