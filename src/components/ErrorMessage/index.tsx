import { View, Text } from "@tarojs/components";
import IconFont from "@/components/iconfont";

interface ErrorMessageProps {
  msg: string;
  icon?: string;
  className?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ msg, icon, className = "" }) => {
  if (!msg) return null;

  return (
    <View className={`flex-col items-center justify-center text-center ${className}`}>
      <View className="text-center">
        <IconFont name={icon} className="text-lg icon-center" size={32} />
        <Text className="text-sm font-medium leading-10">{msg}</Text>
      </View>
    </View>
  );
};

export default ErrorMessage;
