import { View, Text } from "@tarojs/components";
import IconFont, { IconNames } from "@/components/iconfont";

interface ErrorMessageProps {
  msg: string;
  icon?: IconNames;
  className?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ msg, icon = "error", className = "" }) => {
  if (!msg) return null;
  return (
    <View className={`flex flex-col items-center justify-center text-center h-screen text-brand-2D2E2C ${className}`}>
      <View className="flex justify-center items-center mb-2">
        <IconFont name={icon} size={32} />
      </View>
      <Text className="text-sm font-medium pb-50">{msg}</Text>
    </View>
  );
};

export default ErrorMessage;
