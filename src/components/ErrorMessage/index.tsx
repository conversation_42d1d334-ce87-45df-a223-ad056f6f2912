import { View, Text } from "@tarojs/components";
import IconFont from "@/components/iconfont";
import { useHeaderBarHeight,useSafeAreaBottom } from "@/hook";
interface ErrorMessageProps {
  msg: string;
  icon?: string;
  className?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ msg, icon, className = "" }) => {
  if (!msg) return null;
  const { headerBarHeight } = useHeaderBarHeight();
  const { safeAreaBottom } = useSafeAreaBottom();
  return (
    <View
      className={`flex flex-col items-center justify-center text-center h-full text-brand-2D2E2C ${className}`}
      style={{ height: `calc(100vh - ${headerBarHeight}px - ${safeAreaBottom}px - 77px )` }}>
      <View className="flex justify-center items-center mb-2">
        <IconFont name={icon} className="text-lg" size={32} />
      </View>
      <Text className="text-sm font-medium pb-50">{msg}</Text>
    </View>
  );
};

export default ErrorMessage;
