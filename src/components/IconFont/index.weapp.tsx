/* tslint:disable */
/* eslint-disable */

import React, { FunctionComponent } from 'react';
import Taro from '@tarojs/taro';

export type IconNames = 'openCatalog' | 'closeCatalog' | 'filter' | 'check' | 'call' | 'sortBy' | 'distance' | 'search' | 'logo' | 'Successful' | 'delete' | 'bianji' | 'checked1' | 'rules' | 'checked' | 'detail' | 'productList' | 'error' | 'avatar' | 'mic' | 'setting' | 'note' | 'orderList' | 'checkbox1' | 'Icon-2' | 'checkbox' | 'back' | 'shoppingbag' | 'customDetail' | 'address' | 'order' | 'scan' | 'customMade' | 'store' | 'close' | 'timeLine' | 'Icon';

interface Props {
  name: IconNames;
  size?: number;
  color?: string | string[];
  style?: React.CSSProperties;
}

const IconFont: FunctionComponent<Props> = (props) => {
  const { name, size, color, style } = props;

  // @ts-ignore
  return <iconfont name={name} size={parseFloat(Taro.pxTransform(size))} color={color} style={style} />;
};

IconFont.defaultProps = {
  size: 14,
};

export default IconFont;
