<!--openCatalog-->
<view wx:if="{{name === 'openCatalog'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M276.2752 550.0928v-61.44h450.56v61.44h-450.56z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M470.8352 294.0928h61.44v450.56h-61.44v-450.56z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--closeCatalog-->
<view wx:if="{{name === 'closeCatalog'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M277.333333 544v-64h469.333334v64h-469.333334z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--filter-->
<view wx:if="{{name === 'filter'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M371.2 416H172.8v-64h185.6c0 6.4-6.4 12.8-6.4 19.2 0 19.2 12.8 38.4 19.2 44.8zM409.6 320c19.2 0 38.4 12.8 51.2 32h64C512 300.8 467.2 256 409.6 256s-108.8 44.8-115.2 96h64c6.4-19.2 25.6-32 51.2-32z m0 108.8c-12.8 0-25.6-6.4-38.4-12.8H300.8c19.2 44.8 57.6 76.8 108.8 76.8 51.2 0 96-32 108.8-76.8H441.6c-6.4 6.4-19.2 12.8-32 12.8zM876.8 352v64H441.6c12.8-12.8 19.2-25.6 19.2-44.8 0-6.4 0-12.8-6.4-19.2h422.4z' fill='{{(isStr ? colors : colors[0]) || 'rgb(45,46,44)'}}' /%3E%3Cpath d='M646.4 640c19.2 0 32 6.4 44.8 25.6h70.4C748.8 608 704 576 646.4 576s-102.4 38.4-115.2 89.6h70.4c6.4-19.2 25.6-25.6 44.8-25.6z m0 108.8c-19.2 0-32-6.4-44.8-25.6H531.2c12.8 51.2 57.6 89.6 115.2 89.6s102.4-38.4 115.2-89.6h-70.4c-12.8 12.8-25.6 25.6-44.8 25.6z' fill='{{(isStr ? colors : colors[1]) || 'rgb(45,46,44)'}}' /%3E%3Cpath d='M876.8 659.2v64h-185.6c6.4-6.4 12.8-19.2 12.8-32s-6.4-25.6-12.8-32h185.6zM588.8 691.2c0 12.8 6.4 25.6 12.8 32H172.8v-64h428.8c-6.4 12.8-12.8 19.2-12.8 32z' fill='{{(isStr ? colors : colors[2]) || 'rgb(45,46,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--check-->
<view wx:if="{{name === 'check'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M407.466667 753.28l-227.626667-227.584 45.653333-45.653333 181.973334 182.016 391.04-391.04 45.653333 45.610666L407.466667 753.28z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--call-->
<view wx:if="{{name === 'call'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M819.2 870.286222c-78.222222 0-157.127111-18.090667-236.771556-54.385778-79.644444-36.238222-152.917333-87.836444-219.761777-154.680888C295.822222 594.375111 244.280889 521.159111 207.985778 441.514667 171.804444 361.813333 153.6 282.567111 153.6 203.662222v-51.2h235.747556l40.504888 181.304889L305.095111 459.662222c16.327111 27.704889 33.962667 54.044444 52.792889 78.904889 18.830222 24.917333 38.684444 47.672889 59.676444 68.266667 19.569778 19.911111 41.130667 38.968889 64.568889 57.059555 23.495111 18.204444 49.777778 36.124444 78.961778 53.873778l128-124.814222 181.304889 38.4v238.933333h-51.2zM269.880889 387.128889l76.8-77.937778-18.659556-79.928889H230.4c2.844444 27.022222 7.452444 53.873778 13.880889 80.497778 6.371556 26.737778 14.904889 52.451556 25.6 77.368889z m368.014222 362.666667a559.445333 559.445333 0 0 0 155.761778 43.747555v-100.295111l-80.042667-17.066667-75.719111 73.614223z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--sortBy-->
<view wx:if="{{name === 'sortBy'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M412.48 437.632l2.304 2.24-2.304 2.24-30.144 30.208-2.304 2.304-2.24-2.24L293.76 388.672v417.472l84.096-83.648 2.24-2.24 32.448 32.448 2.304 2.24-2.304 2.24-141.12 141.184-2.24 2.24-2.24-2.24-141.12-141.184-2.24-2.24 34.688-34.688 2.24 2.24 84.16 83.712V388.608L160.448 472.384l-2.24 2.24-2.24-2.304-32.448-32.448 145.6-145.6 143.36 143.36z m486.72 369.92v59.008l-3.264-0.128-367.616-9.792-3.136-0.064v-49.024h374.016z m0-234.688v49.088H525.184V572.8h374.016z m0-185.6H525.184v-48.96l3.136-0.128 367.616-9.792 3.2-0.064v59.008z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--distance-->
<view wx:if="{{name === 'distance'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 927.687111c-73.955556 0-134.371556-10.638222-181.248-31.857778-46.933333-21.219556-70.371556-48.583111-70.371556-82.204444 0-15.530667 5.859556-30.151111 17.521778-43.804445 11.719111-13.710222 27.875556-25.486222 48.526222-35.271111l51.712 46.876445a283.136 283.136 0 0 0-29.582222 14.051555c-10.296889 5.575111-17.408 11.491556-21.219555 17.749334 7.281778 13.084444 28.785778 24.462222 64.625777 34.190222 35.84 9.728 75.776 14.620444 119.808 14.620444 44.088889 0 84.195556-4.892444 120.433778-14.620444 36.181333-9.671111 57.856-21.105778 65.137778-34.190222-3.754667-6.656-11.207111-12.8-22.471111-18.432a334.165333 334.165333 0 0 0-32.028445-14.051556l51.029334-47.559111c22.357333 10.24 39.594667 22.243556 51.655111 35.953778 12.060444 13.653333 18.090667 28.444444 18.090667 44.373333 0 33.564444-23.438222 61.041778-70.371556 82.261333-46.876444 21.276444-107.292444 31.857778-181.248 31.857778z m1.137778-213.333333c72.419556-54.954667 126.862222-109.340444 163.157333-163.271111 36.352-53.873778 54.499556-107.349333 54.499556-160.483556 0-75.491556-23.608889-132.551111-70.883556-171.008-47.274667-38.456889-96.483556-57.742222-147.683555-57.742222-51.2 0-100.522667 19.285333-147.911112 57.799111-47.388444 38.513778-71.111111 95.573333-71.111111 171.064889 0 49.607111 17.92 101.091556 53.816889 154.396444 35.84 53.304889 91.249778 109.681778 166.115556 169.244445zM512 796.387556c-95.516444-71.395556-166.798222-140.743111-213.845333-207.985778C251.107556 521.102222 227.555556 455.224889 227.555556 390.656c0-48.753778 8.590222-91.420444 25.770666-128.056889a287.687111 287.687111 0 0 1 66.616889-92.046222 277.333333 277.333333 0 0 1 91.591111-55.694222 290.133333 290.133333 0 0 1 100.522667-18.659556c33.166222 0 66.673778 6.257778 100.465778 18.659556 33.792 12.458667 64.284444 31.004444 91.534222 55.751111 27.192889 24.689778 49.379556 55.352889 66.56 92.046222 17.237333 36.636444 25.827556 79.303111 25.827555 127.943111 0 64.568889-23.552 130.446222-70.599111 197.745778-47.104 67.299556-118.328889 136.647111-213.845333 208.042667z m0.227556-332.8c21.617778 0 40.220444-7.68 55.694222-22.983112 15.473778-15.36 23.210667-33.962667 23.210666-55.921777s-7.793778-40.675556-23.267555-56.149334A76.231111 76.231111 0 0 0 512 305.322667c-21.560889 0-40.106667 7.793778-55.751111 23.267555-15.530667 15.473778-23.324444 34.133333-23.324445 55.864889 0 22.129778 7.793778 40.789333 23.324445 56.149333 15.644444 15.303111 34.304 22.983111 55.978667 22.983112z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--search-->
<view wx:if="{{name === 'search'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M469.3504 123.7504c47.6672 0 92.5184 9.0112 134.4512 27.0336a348.8768 348.8768 0 0 1 109.9264 74.1376A349.0304 349.0304 0 0 1 787.968 334.848c18.0224 41.984 27.0336 86.784 27.0336 134.5024 0 43.8272-7.7312 85.1968-23.1424 124.0576a358.5024 358.5024 0 0 1-61.2352 101.6832l168.448 168.4992 3.072 3.072-3.072 2.9696-29.6448 29.44-3.072 2.9184-2.9696-2.9696-168.448-168.2944a356.4544 356.4544 0 0 1-101.7344 61.2352c-38.8608 15.36-80.128 22.9888-123.8016 22.9888a337.408 337.408 0 0 1-134.7584-27.0336 348.5696 348.5696 0 0 1-109.7728-74.0864 348.4672 348.4672 0 0 1-74.0352-109.7728 337.2544 337.2544 0 0 1-27.0336-134.7072c0-47.7184 9.0112-92.5696 27.0336-134.5024a349.8496 349.8496 0 0 1 74.0352-109.8752 347.4432 347.4432 0 0 1 109.7728-74.1888 337.3568 337.3568 0 0 1 134.7584-27.0336z m0 51.2c-81.5104 0-150.8864 28.672-208.2816 86.2208-57.4464 57.4976-86.1184 126.8224-86.1184 208.1792 0 81.5104 28.672 150.8864 86.1184 208.2816 57.3952 57.4464 126.7712 86.1184 208.2816 86.1184 81.3568 0 150.6304-28.672 208.128-86.1184 57.4976-57.3952 86.272-126.7712 86.272-208.2816 0-81.3568-28.7232-150.6816-86.272-208.1792-57.4976-57.4976-126.7712-86.2208-208.128-86.2208z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--logo-->
<view wx:if="{{name === 'logo'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 9721 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M211.378643 674.341164c0 37.344034 8.031525 69.163196 23.685326 95.917894 15.6538 26.243136 37.344034 47.933369 65.633417 65.121856a309.188135 309.188135 0 0 0 96.429457 36.832471c36.320909 7.571119 75.711191 11.61246 118.068533 11.61246 45.938277 0 84.816997-4.552903 117.659284-13.147146 32.228412-8.594243 58.522705-19.695141 78.729407-33.302693 20.155547-13.607552 34.786223-29.261352 43.380467-47.472963 9.105805-17.648893 13.658708-35.809347 13.658708-53.969802 0-37.344034-10.640492-65.121856-31.307601-82.770748a181.195297 181.195297 0 0 0-68.140072-37.344034 1783.407803 1783.407803 0 0 0-148.404166-36.320909 15748.337024 15748.337024 0 0 1-208.973119-44.505903c-51.463148-11.049741-94.383208-25.220012-128.197463-42.868904-34.325817-18.160455-61.080515-37.855596-81.287218-59.545829a196.439847 196.439847 0 0 1-42.868904-69.674758 228.412479 228.412479 0 0 1-12.635584-75.199629c0-49.468055 12.635584-91.876554 37.855595-127.685901A307.49998 307.49998 0 0 1 184.623945 70.084008 481.993813 481.993813 0 0 1 325.405836 17.648893 739.05377 739.05377 0 0 1 484.9109 0c62.615201 0 121.649468 6.547995 177.205112 19.695141 55.504488 13.09599 103.949419 33.302693 145.846356 59.545829 41.948092 26.754698 75.199629 59.085423 99.959234 97.912986 24.70845 38.87872 36.832471 83.793872 36.832472 134.796614h-209.945087c-5.627183-63.126763-31.819163-107.530354-78.780564-133.261927-46.910245-25.731574-106.967636-38.87872-179.660611-38.87872-24.759606 0-49.979617 2.046248-75.711191 6.087589a274.657693 274.657693 0 0 0-70.697882 20.667109c-21.178671 9.617368-38.87872 22.713357-52.997834 38.87872-14.119114 16.625768-21.178671 36.832471-21.178671 61.592077 0 34.786223 13.09599 61.540921 39.390282 81.236062 26.243136 19.695141 60.517797 34.325817 103.437857 44.40359 4.552903 1.023124 22.201795 4.604059 52.997834 11.61246 30.796039 6.599151 65.121856 14.17027 103.489013 22.713357 37.855596 8.082681 75.199629 16.165362 111.520539 23.736482 36.320909 7.571119 62.615201 13.607552 78.269001 17.188486 39.390282 10.077773 73.153381 23.685325 102.465889 40.873812 29.261352 17.13733 53.509396 37.344034 73.153381 59.545829 19.695141 22.201795 34.325817 46.449839 43.431623 71.669851 9.617368 25.731574 14.119114 51.002742 14.119114 76.734315 0 54.53252-13.607552 101.442765-41.385374 140.321485-27.26626 38.87872-63.587169 70.135164-108.502322 94.89477-44.915153 24.70845-95.917894 42.408498-152.957069 53.969802A910.580543 910.580543 0 0 1 500.5647 1023.124206a879.886817 879.886817 0 0 1-193.319319-20.667109c-60.568953-13.658708-113.055225-34.837379-157.970377-63.126764a329.599463 329.599463 0 0 1-107.530354-108.502322c-26.754698-44.403591-40.873812-96.429456-41.896936-156.486847h211.530929zM3813.236253 674.238852c0 37.344034 8.082681 69.163196 23.736482 95.917894 15.6538 26.243136 37.344034 47.933369 65.582261 65.121856a309.188135 309.188135 0 0 0 96.429457 36.832471c36.320909 7.571119 75.711191 11.61246 118.119689 11.61246 45.938277 0 84.816997-4.604059 117.608128-13.147146 32.279569-8.594243 58.522705-19.695141 78.729407-33.302693s34.786223-29.261352 43.431623-47.472963c9.054649-17.648893 13.607552-35.809347 13.607552-53.969802 0-37.344034-10.589336-65.121856-31.307601-82.770749a181.195297 181.195297 0 0 0-68.140072-37.344033 1787.346831 1787.346831 0 0 0-148.35301-36.372066 15654.823471 15654.823471 0 0 1-208.973119-44.40359c-51.514304-11.100898-94.434364-25.271168-128.248619-42.92006-34.274661-17.648893-61.080515-37.344034-81.236062-59.545829a196.439847 196.439847 0 0 1-42.92006-69.674759 226.263918 226.263918 0 0 1-12.584428-75.199629c0-49.468055 12.584428-91.876554 37.855595-127.685901a307.448824 307.448824 0 0 1 99.908079-89.881461A482.04497 482.04497 0 0 1 3927.314602 17.54658a738.542208 738.542208 0 0 1 159.505064-17.13733c62.564045 0 121.649468 6.547995 177.153956 19.695141 55.504488 13.09599 104.000576 33.251537 145.897512 59.545828 41.896936 26.754698 75.199629 59.034267 99.908078 97.912987 24.759606 38.87872 36.832471 83.793872 36.832472 134.745458h-209.945087c-5.576027-63.075607-31.819163-107.479198-78.780564-133.210772-46.910245-25.78273-106.967636-38.87872-179.660611-38.87872-24.70845 0-49.979617 2.046248-75.711191 6.036433a274.657693 274.657693 0 0 0-70.646726 20.718265c-21.229827 9.566211-38.87872 22.713357-52.997834 38.87872-14.119114 16.625768-21.229827 36.832471-21.229827 61.540921 0 34.837379 13.147146 61.592077 39.390282 81.287218s60.568953 34.274661 103.489013 44.403591c4.501747 1.023124 22.201795 4.552903 52.997834 11.61246 30.796039 6.547995 65.121856 14.119114 103.437857 22.713357 37.855596 8.082681 75.199629 16.165362 111.520538 23.736481 36.372066 7.571119 62.615201 13.607552 78.269002 17.137331 39.390282 10.12893 73.153381 23.736482 102.465889 40.924968a299.26383 299.26383 0 0 1 73.153381 59.545829c19.746297 22.201795 34.376973 46.449839 43.482779 71.618694 9.566211 25.78273 14.119114 51.002742 14.119114 76.734316 0 54.53252-13.658708 101.493921-41.43653 140.372641-27.215104 38.87872-63.587169 70.135164-108.451166 94.89477-44.966309 24.70845-95.917894 42.357342-152.957069 53.969802-57.039174 11.100898-115.101473 17.188487-174.647302 17.188486a879.886817 879.886817 0 0 1-193.370475-20.718265c-60.517797-13.607552-113.055225-34.786223-157.970377-63.075607a329.599463 329.599463 0 0 1-107.479198-108.502322c-26.754698-44.454747-40.924968-96.429456-41.948092-156.538004h211.530929v-0.460405zM1136.538706 23.787638h199.91847v568.857058c0 32.791131 2.046248 66.093824 5.524871 99.908079a206.159527 206.159527 0 0 0 31.819162 90.853429c17.648893 26.754698 44.403591 48.956493 79.752532 66.14498 35.809347 17.13733 85.788965 25.731574 150.910821 25.731574 65.121856 0 115.101473-8.594243 150.91082-25.731574 35.809347-17.13733 62.103639-39.390282 79.803688-66.14498a200.020782 200.020782 0 0 0 31.71685-90.853429c3.580935-33.763099 5.627183-67.116948 5.627184-99.908079V23.787638h199.867313V648.660746c0 62.564045-11.152054 117.608127-33.353849 164.057967a323.614186 323.614186 0 0 1-94.89477 117.096565c-40.873812 31.307601-90.341867 54.992926-147.841448 70.646727-57.601893 15.6538-121.700624 23.224919-191.835788 23.224919-70.186321 0-134.285052-7.571119-191.835789-23.224919-57.49958-15.6538-106.967636-38.87872-147.841448-70.646727a319.470533 319.470533 0 0 1-94.89477-117.147721c-22.201795-46.398683-33.353849-101.442765-33.353849-164.006811V23.787638zM2302.030645 23.787638h199.867314v976.674367h-199.867314V23.787638zM2644.265692 23.787638h923.164971v147.841448h-362.390594v828.832919h-199.91847V171.16868h-360.855907V23.787638zM4737.93591 23.787638h199.867314v568.857058c0 32.791131 2.046248 66.093824 5.576027 99.908079a206.159527 206.159527 0 0 0 31.768006 90.853429c17.700049 26.754698 44.454747 48.956493 79.803688 66.14498 35.809347 17.13733 85.788965 25.731574 150.910821 25.731574 65.070699 0 115.050317-8.594243 150.91082-25.731574 35.809347-17.13733 62.052483-39.390282 79.752532-66.14498a200.071938 200.071938 0 0 0 31.768006-90.853429c3.529779-33.763099 5.576027-67.116948 5.576027-99.908079V23.787638h199.867314V648.660746c0 62.564045-11.100898 117.608127-33.302693 164.057967a323.614186 323.614186 0 0 1-94.89477 117.096565c-40.873812 31.307601-90.341867 54.992926-147.892604 70.646727-57.550737 15.6538-121.137906 23.224919-191.784632 23.224919-70.186321 0-134.285052-7.571119-191.835789-23.224919-57.550737-15.6538-107.018792-38.87872-147.841448-70.646727a319.470533 319.470533 0 0 1-94.945926-117.147721c-22.201795-46.398683-33.302693-101.442765-33.302693-164.006811V23.787638zM5902.916287 24.196887h503.223641c80.775656 0 146.86948 10.077773 198.332627 30.284477 51.002742 20.155547 90.393024 44.915153 118.119689 75.199629 27.777822 30.284476 46.449839 63.075607 56.527613 98.424549 10.12893 35.297785 15.142238 68.140072 15.142238 98.424548 0 30.284476-5.013309 62.564045-15.142238 97.912987-10.077773 35.297785-28.74979 67.62851-56.527613 97.912986-27.726666 30.284476-67.116948 55.044082-118.119689 74.688067-50.951585 19.695141-117.096565 29.312508-198.332627 29.312509h-303.356327v374.975021h-199.867314V24.248044z m199.867314 462.349829h292.255429c22.201795 0 45.426715-3.069373 68.651634-8.082681a221.250609 221.250609 0 0 0 64.610294-26.754698c19.643985-12.124022 35.809347-28.800946 47.933369-49.468056 12.124022-20.718265 18.160455-46.449839 18.160454-77.245877 0-31.768007-5.524871-58.522705-16.165362-79.24097a134.029271 134.029271 0 0 0-42.868904-49.979617 169.531681 169.531681 0 0 0-62.10364-25.220012 392.368133 392.368133 0 0 0-75.199629-7.059557h-295.785208V486.49556h0.511563zM6952.283629 24.196887h503.22364c80.775656 0 146.86948 10.077773 198.383784 30.284477 50.951585 20.155547 90.341867 44.915153 118.068533 75.199629 27.777822 30.284476 46.449839 63.075607 56.527612 98.424549 10.12893 35.297785 15.142238 68.140072 15.142239 98.424548 0 30.284476-5.013309 62.564045-15.142239 97.912987-10.077773 35.297785-28.74979 67.62851-56.527612 97.912986-27.726666 30.284476-67.116948 55.044082-118.068533 74.688067-51.002742 19.695141-117.147722 29.312508-198.383784 29.312509h-303.356327v374.975021h-199.867313V24.248044z m199.867313 462.349829h292.255429c22.201795 0 45.426715-3.069373 68.651635-8.082681a221.250609 221.250609 0 0 0 64.610293-26.754698c19.643985-12.124022 35.809347-28.800946 47.933369-49.468056 12.124022-20.718265 18.160455-46.449839 18.160455-77.245877 0-31.768007-5.524871-58.522705-16.165362-79.24097a134.029271 134.029271 0 0 0-42.868905-49.979617 169.531681 169.531681 0 0 0-62.103639-25.220012 392.368133 392.368133 0 0 0-75.199629-7.059557h-295.785208V486.49556h0.511562zM8001.65097 22.764514h199.867314v828.832919h579.957956v147.841447h-779.82527V22.81567zM9066.16055 617.353146L8638.136538 23.787638h231.686477l301.821641 437.590223 297.319894-437.590223h223.603795l-426.540482 593.565508v383.160015h-199.867313v-383.160015z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Successful-->
<view wx:if="{{name === 'Successful'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M430.98193 733.506905l358.524415-358.524415-56.191438-56.191438-302.332977 302.332977L278.98345 469.125549l-56.191438 56.191438 208.189918 208.189918z m75.775243 279.805202a493.563064 493.563064 0 0 1-197.630024-39.871601 511.738883 511.738883 0 0 1-160.894391-108.286918 511.610884 511.610884 0 0 1-108.286917-160.830391A493.115069 493.115069 0 0 1 0.01024 506.757172C0.01024 436.677873 13.322107 370.822532 39.881841 309.127149a511.738883 511.738883 0 0 1 108.286917-160.894391A511.674883 511.674883 0 0 1 308.99915 39.945841 493.115069 493.115069 0 0 1 506.565174 0.01024c70.079299 0 135.934641 13.311867 197.630024 39.871601 61.631384 26.623734 115.262847 62.719373 160.894391 108.286917a511.674883 511.674883 0 0 1 108.350917 160.830392c26.559734 61.631384 39.871601 127.486725 39.871601 197.566024a493.563064 493.563064 0 0 1-39.871601 197.630024 511.802882 511.802882 0 0 1-108.286917 160.894391 511.610884 511.610884 0 0 1-160.830392 108.350917 493.115069 493.115069 0 0 1-197.566024 39.871601z m-0.064-79.9992c119.03881 0 219.9658-41.343587 302.652974-123.96676 82.623174-82.687173 123.96676-183.550164 123.96676-302.716973 0-119.03881-41.343587-219.9658-123.96676-302.652974-82.687173-82.623174-183.550164-123.96676-302.716973-123.96676-119.03881 0-219.9658 41.343587-302.652974 124.03076C121.417026 286.663373 80.00944 387.590364 80.00944 506.693173c0 119.03881 41.343587 219.9658 124.03076 302.652974 82.623174 82.623174 183.550164 123.96676 302.652973 123.96676z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--delete-->
<view wx:if="{{name === 'delete'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M617.6 176.832v46.208h183.488v61.888h-51.2v495.232c0 20.48-7.04 37.76-20.864 51.584a69.696 69.696 0 0 1-50.944 20.608h-332.16c-19.776 0-36.736-7.04-50.752-20.992a69.312 69.312 0 0 1-21.056-50.752V284.928h-51.2v-61.888H406.4v-46.208h211.2z m-281.6 603.776l0.64 4.096a9.088 9.088 0 0 0 2.112 3.136 9.664 9.664 0 0 0 7.168 2.688h332.16c2.24 0 4.48-0.896 6.72-3.2a9.408 9.408 0 0 0 3.2-6.72V284.928h-352v495.68z m149.504-425.28v364.8h-61.888v-364.8h61.888z m114.88 0v364.8h-61.888v-364.8h61.888z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--bianji-->
<view wx:if="{{name === 'bianji'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M872.362667 850.176v80.213333H160v-80.213333h712.362667zM715.946667 108.373333a51.285333 51.285333 0 0 1 47.957333-13.312c13.056 2.645333 26.453333 9.301333 39.253333 19.285334l12.373334 11.093333 34.133333 34.133333c7.338667 7.424 17.92 19.968 24.746667 34.304v0.085334c5.546667 11.946667 15.786667 41.813333-7.68 65.194666l-443.733334 443.733334a35.413333 35.413333 0 0 1-11.349333 7.424l-170.837333 68.266666a34.133333 34.133333 0 0 1-44.970667-42.496l51.2-153.6a34.133333 34.133333 0 0 1 8.192-13.397333l460.8-460.8zM309.333333 611.669333l-25.344 76.117334L379.733333 649.386667l336.725334-336.725334-54.016-54.186666-353.194667 353.109333z m401.408-401.408l54.186667 54.016 45.226667-45.226666a72.533333 72.533333 0 0 0-8.618667-11.264l-34.133333-34.133334a63.744 63.744 0 0 0-11.008-9.045333l-45.653334 45.653333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--checked1-->
<view wx:if="{{name === 'checked1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M128 0a128 128 0 0 0-128 128v768a128 128 0 0 0 128 128h768a128 128 0 0 0 128-128V128a128 128 0 0 0-128-128H128z m279.68 776.32L156.16 524.8l69.12-69.12 182.4 181.76 391.04-391.04 69.12 69.12-460.16 460.8z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--rules-->
<view wx:if="{{name === 'rules'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M98.133333 755.2h827.733334V311.466667H98.133333v443.733333zM981.333333 768a42.666667 42.666667 0 0 1-42.666666 42.666667H85.333333a42.666667 42.666667 0 0 1-42.666666-42.666667V298.666667a42.666667 42.666667 0 0 1 42.666666-42.666667h853.333334a42.666667 42.666667 0 0 1 42.666666 42.666667v469.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M170.666667 506.88a21.333333 21.333333 0 0 1 42.666666 0v64a21.333333 21.333333 0 0 1-42.666666 0v-64zM277.333333 464.213333a21.333333 21.333333 0 0 1 42.666667 0v149.333334a21.333333 21.333333 0 0 1-42.666667 0v-149.333334zM384 506.88a21.333333 21.333333 0 0 1 42.666667 0v64a21.333333 21.333333 0 0 1-42.666667 0v-64zM490.666667 389.12a21.333333 21.333333 0 0 1 42.666666 0v298.666667a21.333333 21.333333 0 0 1-42.666666 0v-298.666667zM597.333333 506.88a21.333333 21.333333 0 0 1 42.666667 0v64a21.333333 21.333333 0 0 1-42.666667 0v-64zM704 464.213333a21.333333 21.333333 0 0 1 42.666667 0v149.333334a21.333333 21.333333 0 0 1-42.666667 0v-149.333334zM810.666667 506.88a21.333333 21.333333 0 0 1 42.666666 0v64a21.333333 21.333333 0 0 1-42.666666 0v-64z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--checked-->
<view wx:if="{{name === 'checked'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 46.545455a465.454545 465.454545 0 1 0 0 930.90909 465.454545 465.454545 0 0 0 0-930.90909zM426.356364 744.727273l-205.265455-221.556364L279.272727 462.196364l148.48 160.116363L744.727273 279.272727l56.785454 60.509091L426.356364 744.727273z' fill='{{(isStr ? colors : colors[0]) || 'rgb(45,46,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--detail-->
<view wx:if="{{name === 'detail'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M426.7008 768l256-256-256-256-59.7504 59.7504L563.2 512l-196.2496 196.2496L426.7008 768z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--productList-->
<view wx:if="{{name === 'productList'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1066 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M298.666667 391.125333h156.458666V234.666667H298.666667v156.458666z m220.458666 32l-0.170666 3.242667a32 32 0 0 1-28.586667 28.586667l-3.242667 0.170666H266.666667l-3.285334-0.170666a32 32 0 0 1-28.586666-28.586667L234.666667 423.125333V202.666667A32 32 0 0 1 266.666667 170.666667h220.458666l3.242667 0.170666a32 32 0 0 1 28.757333 31.829334v220.458666zM298.666667 746.624h156.458666v-156.458667H298.666667v156.458667z m220.458666 32l-0.170666 3.242667a32 32 0 0 1-28.586667 28.586666l-3.242667 0.170667H266.666667l-3.285334-0.170667a32 32 0 0 1-28.586666-28.586666L234.666667 778.666667v-220.458667a32 32 0 0 1 32-32h220.458666l3.242667 0.170667a32 32 0 0 1 28.757333 31.829333v220.458667zM654.208 391.125333H810.666667V234.666667h-156.458667v156.458666z m220.458667 32l-0.170667 3.242667a32 32 0 0 1-28.586667 28.586667l-3.242666 0.170666h-220.458667l-3.285333-0.170666a32 32 0 0 1-28.586667-28.586667l-0.128-3.242667V202.666667a32 32 0 0 1 32-32h220.458667l3.242666 0.170666a32 32 0 0 1 28.757334 31.829334v220.458666zM654.208 746.624H810.666667v-156.458667h-156.458667v156.458667z m220.458667 32l-0.170667 3.242667a32 32 0 0 1-28.586667 28.586666L842.666667 810.666667h-220.458667l-3.285333-0.170667a32 32 0 0 1-28.586667-28.586667l-0.128-3.242666v-220.458667a32 32 0 0 1 32-32h220.458667l3.242666 0.170667a32 32 0 0 1 28.757334 31.829333v220.458667z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--error-->
<view wx:if="{{name === 'error'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 691.552a29.664 29.664 0 0 0 21.856-8.832 29.664 29.664 0 0 0 8.8-21.824 29.664 29.664 0 0 0-8.8-21.856 29.696 29.696 0 0 0-21.856-8.8 29.696 29.696 0 0 0-21.856 8.8 29.664 29.664 0 0 0-8.8 21.856c0 8.672 2.912 15.968 8.8 21.856a29.664 29.664 0 0 0 21.856 8.8z m-28.48-138.688h56.96V325.12h-56.96v227.744z m28.544 319.712c-49.888 0-96.768-9.472-140.64-28.384a364.256 364.256 0 0 1-114.528-77.056 364.224 364.224 0 0 1-77.12-114.496 350.976 350.976 0 0 1-28.384-140.608c0-49.856 9.472-96.736 28.384-140.64a364.224 364.224 0 0 1 77.088-114.496 364.16 364.16 0 0 1 114.464-77.12 350.976 350.976 0 0 1 140.608-28.416c49.856 0 96.768 9.472 140.64 28.416a364.224 364.224 0 0 1 114.528 77.056 364.192 364.192 0 0 1 77.12 114.464c18.88 43.872 28.384 90.752 28.384 140.608 0 49.888-9.472 96.768-28.416 140.64a364.192 364.192 0 0 1-77.056 114.56 364.256 364.256 0 0 1-114.464 77.088c-43.872 18.912-90.752 28.384-140.608 28.384zM512 815.616c84.768 0 156.576-29.376 215.424-88.224 58.816-58.848 88.224-130.656 88.224-215.424s-29.408-156.576-88.256-215.392C668.576 237.728 596.8 208.32 512 208.32c-84.8 0-156.576 29.44-215.424 88.256C237.76 355.392 208.32 427.2 208.32 511.968c0 84.8 29.44 156.576 88.256 215.424 58.848 58.848 130.656 88.256 215.424 88.256z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--avatar-->
<view wx:if="{{name === 'avatar'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M963.524224 270.651451a511.972024 511.972024 0 1 0-813.486977 603.3956c9.142358 8.776663 18.284715 17.187632 27.427073 25.415754a513.069107 513.069107 0 0 0 334.610287 124.51891h7.496733a509.595011 509.595011 0 0 0 265.12837-79.355663 511.972024 511.972024 0 0 1-606.321154-46.077483v-28.88985a134.209809 134.209809 0 0 1 132.564185-134.026962h413.417409a134.209809 134.209809 0 0 1 51.562897 10.056594 134.209809 134.209809 0 0 1 83.561148 123.970368v18.284716c4.936873-4.388332 9.873746-9.142358 14.627772-13.896384a511.972024 511.972024 0 0 0 89.412257-603.3956z m-312.120087 336.073064a190.874142 190.874142 0 0 1-133.478421 59.608172 191.075273 191.075273 0 0 1-185.772706-196.012147 191.075273 191.075273 0 0 1 185.772706-196.012146 191.075273 191.075273 0 0 1 185.772706 196.012146 191.440968 191.440968 0 0 1-52.294285 136.403975z' fill='{{(isStr ? colors : colors[0]) || 'rgb(225,225,225)'}}' /%3E%3Cpath d='M963.524224 270.651451a511.972024 511.972024 0 1 0-813.486977 603.3956c9.142358 8.776663 18.284715 17.187632 27.427073 25.415754a513.069107 513.069107 0 0 0 334.610287 124.51891h7.496733a509.595011 509.595011 0 0 0 265.12837-79.355663 511.972024 511.972024 0 0 1-606.321154-46.077483v-28.88985a134.209809 134.209809 0 0 1 132.564185-134.026962h413.417409a134.209809 134.209809 0 0 1 51.562897 10.056594 134.209809 134.209809 0 0 1 83.561148 123.970368v18.284716c4.936873-4.388332 9.873746-9.142358 14.627772-13.896384a511.972024 511.972024 0 0 0 89.412257-603.3956z m-312.120087 336.073064a190.874142 190.874142 0 0 1-133.478421 59.608172 191.075273 191.075273 0 0 1-185.772706-196.012147 191.075273 191.075273 0 0 1 185.772706-196.012146 191.075273 191.075273 0 0 1 185.772706 196.012146 191.440968 191.440968 0 0 1-52.294285 136.403975z' fill='{{(isStr ? colors : colors[1]) || 'rgb(225,225,225)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--mic-->
<view wx:if="{{name === 'mic'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 4544 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M0 0h458.24v458.24H0V0z' fill='{{(isStr ? colors : colors[0]) || 'rgb(242,107,49)'}}' /%3E%3Cpath d='M507.648 0h458.24v458.24h-458.24V0z' fill='{{(isStr ? colors : colors[1]) || 'rgb(144,192,82)'}}' /%3E%3Cpath d='M0 510.272h458.24v458.24H0v-458.24z' fill='{{(isStr ? colors : colors[2]) || 'rgb(62,172,221)'}}' /%3E%3Cpath d='M507.648 510.272h458.24v458.24h-458.24v-458.24z' fill='{{(isStr ? colors : colors[3]) || 'rgb(250,191,27)'}}' /%3E%3Cpath d='M1574.848 624.448l-171.392-429.76h-142.848v579.008h94.784V325.824l184.32 447.872h68.864l179.2-447.872v447.872h102.528V194.688h-138.88l-176.64 429.76z m407.68-266.176h99.904v418.048h-99.968V358.272z m329.728 0c-81.792 23.36-173.952 81.792-158.4 248 15.552 166.144 184.32 181.76 223.36 179.2 38.912-2.624 99.904-31.232 99.904-31.232v-86.976s-48 48-123.328 33.728c-76.608-10.368-99.968-84.352-99.968-112.896 0-28.608-5.184-138.88 102.592-158.4 86.976-15.616 118.144 33.728 118.144 33.728v-92.16c-2.624 0-84.416-40.256-162.304-12.992z m333.632 67.52V354.368h-97.344v418.048h99.968V564.736c0-58.432 7.808-107.776 60.992-123.392 53.248-15.552 81.792 15.616 81.792 15.616V354.368c-106.432-23.36-145.408 71.424-145.408 71.424z m371.328-77.888c-131.136-2.56-189.568 94.72-189.568 94.72-89.6 215.552 41.6 340.224 174.016 340.224s220.672-79.232 223.296-218.112c0-136.32-76.608-214.272-207.744-216.832z m55.808 338.816c-38.912 20.8-138.88 31.168-166.144-60.992-25.984-92.16 7.744-155.776 36.352-179.2 0 0 31.168-23.36 79.168-18.112 48 5.12 92.16 23.36 99.968 99.968 7.808 76.544-10.368 137.6-49.28 158.336z m730.944-338.816c-131.136-2.56-189.568 94.72-189.568 94.72-89.6 215.552 41.6 340.224 174.016 340.224s220.672-79.232 223.296-218.112c0-136.32-75.328-214.272-207.744-216.832z m55.808 338.816c-38.912 20.8-138.88 31.168-166.144-60.992-25.984-92.16 7.808-155.776 36.352-179.2 0 0 31.168-23.36 79.168-18.112 48 5.12 92.16 23.36 99.968 99.968 7.808 76.544-12.992 137.6-49.28 158.336z m-442.688-168.768c-23.36-7.744-71.424-36.352-33.728-84.352 25.92-31.168 142.784 18.176 142.784 18.176v-88.32s-73.984-31.168-166.208-7.744c-92.16 23.36-120.704 147.968-60.992 197.312 60.992 50.624 160.96 63.616 148.032 118.144-12.992 55.808-115.584 33.728-179.2-5.184v97.344s94.784 33.792 173.952 12.992c79.232-20.8 112.96-76.608 105.216-142.784-2.624-67.52-109.056-107.776-129.856-115.584zM4544 358.272h-99.968V232.32l-97.344 31.168v92.16h-150.656V281.728s10.432-76.608 107.776-38.912v-87.04s-73.984-18.112-133.76 15.616c-60.992 33.728-66.176 87.04-68.736 110.336-2.624 23.36 0 76.608 0 76.608h-71.424v81.792h68.8v334.976h99.968V440.064h145.408v240.192s12.992 66.24 44.16 87.04c31.168 20.736 102.528 33.664 155.776 5.12v-76.608s-41.6 18.176-63.616 12.992c-23.36-5.12-36.352-25.92-36.352-66.176V445.248H4544V358.272zM1976 245.312a58.304 58.304 0 0 0 66.176 48 58.304 58.304 0 0 0 48.064-66.176 58.304 58.304 0 0 0-66.24-48 57.344 57.344 0 0 0-48 66.176z' fill='{{(isStr ? colors : colors[4]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--setting-->
<view wx:if="{{name === 'setting'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M218.6752 342.784v338.5344L512 850.688l293.3248-169.3696V342.784L512 173.312l-293.3248 169.472z m648.0384 344.5248l-0.512 6.7584a51.2 51.2 0 0 1-25.088 37.5808L537.6 906.8544l-6.144 3.0208a51.2 51.2 0 0 1-45.056-3.0208L182.8864 731.648a51.2 51.2 0 0 1-25.1392-37.5808l-0.4608-6.7584V336.7936a51.2 51.2 0 0 1 25.6-44.3392L486.4 117.248a51.2 51.2 0 0 1 51.2 0l303.5136 175.2064a51.2 51.2 0 0 1 25.6 44.3392v350.5152z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M691.2 481.3824V542.72h-358.4V481.28h358.4z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--note-->
<view wx:if="{{name === 'note'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M351.3856 260.1472h-96.256v621.056h524.8V260.096h-96.256v-46.8992l0.1536-0.3584a3.4816 3.4816 0 0 0 0-2.3552l-1.024-2.9696h99.3792a50.432 50.432 0 0 1 50.3808 50.3296v625.5104a50.3808 50.3808 0 0 1-50.3808 50.3296H252.928a50.3808 50.3808 0 0 1-50.3296-50.3296V257.8944a50.3808 50.3808 0 0 1 50.3296-50.3296h99.4304l-1.024 2.9696a3.584 3.584 0 0 0 0 2.3552l0.1024 0.3584v46.8992z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M420.608 402.0736l33.7408 33.6384 3.1232 3.1232-3.1232 3.2256-89.088 89.4976-3.1232 3.1232-3.1744-3.1744-64.9216-65.3824-3.1744-3.1744 3.1744-3.1744 33.6384-33.6896 3.1232-3.1232 3.1744 3.072 28.16 27.6992 52.224-51.712 3.072-3.1232 3.1744 3.1744zM420.608 546.2528l33.7408 33.6384 3.1232 3.1744-3.1232 3.1744-89.088 89.4976-3.1232 3.1744-3.1744-3.2256-64.9216-65.3824-3.1744-3.1744 3.1744-3.1232 33.6384-33.6896 3.1232-3.1744 3.1744 3.072 28.16 27.7504 52.224-51.712 3.072-3.1744 3.1744 3.1744zM420.608 690.688l33.7408 33.5872 3.1232 3.1744-3.1232 3.2256-89.088 89.4976-3.1232 3.1232-3.1744-3.1744-64.9216-65.4336-3.1744-3.1232 3.1744-3.1744 33.6384-33.6896 3.1232-3.1232 3.1744 3.072 28.16 27.6992 52.224-51.712 3.072-3.1232 3.1744 3.1232zM703.488 417.536V474.624H502.016V417.536h201.3696zM703.488 561.9712v57.0368H502.016v-57.0368h201.3696zM703.488 706.3552v57.0368H502.016v-57.0368h201.3696z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M612.352 107.1616c15.4624 0.1024 30.5152 4.9664 43.1104 13.8752l5.632 4.3008c5.12 4.4032 9.472 9.5232 13.1072 15.2064 15.3088 2.2016 29.5424 9.0624 40.704 19.712l4.7616 5.12c10.2912 12.3904 16.128 27.904 16.5376 44.1344l0.3072 2.4064c0 0.6144-0.1024 1.2288-0.256 1.8944v48.128a74.5472 74.5472 0 0 1-45.6704 69.632 74.496 74.496 0 0 1-28.672 5.7344H373.248a74.496 74.496 0 0 1-74.496-74.3936v-48.128a11.4176 11.4176 0 0 1 0.0512-4.352c0.512-18.5344 8.1408-36.1472 21.2992-49.152 9.728-9.6768 22.016-16.4352 35.328-19.6096 5.12-8.448 12.0832-15.6672 20.3264-21.1456 12.9536-8.9088 28.3648-13.568 44.1344-13.312v-0.0512h192.512zM351.3856 261.888l0.512 4.2496a22.4256 22.4256 0 0 0 21.3504 17.6128h288.5632a22.3744 22.3744 0 0 0 21.9136-21.76l-0.9728-48.128v-0.4096l0.1024-0.4096a3.584 3.584 0 0 0 0-2.3552l-0.256-0.6144 0.1024-0.0512a15.5648 15.5648 0 0 0-5.4272-11.3152l-0.2048-0.2048a20.48 20.48 0 0 0-15.1552-6.5024h-18.0736l-0.6656-1.024-10.1376-16.384-1.6896-3.328a27.0336 27.0336 0 0 0-6.4512-7.68h-0.0512a22.784 22.784 0 0 0-9.216-3.584l-3.2768-0.256H419.84a20.48 20.48 0 0 0-12.1344 3.8912l-0.1024 0.0512a21.8112 21.8112 0 0 0-8.192 10.496l-0.1536 0.2048-8.192 16.384-0.6144 1.2288H372.224a20.48 20.48 0 0 0-15.2064 6.5024l-0.1536 0.2048-1.536-1.6896 1.536 1.6896a15.616 15.616 0 0 0-5.4784 11.3664v0.6656H351.232a3.584 3.584 0 0 0 0 2.304l0.1536 0.4096v48.4352z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--orderList-->
<view wx:if="{{name === 'orderList'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1066 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M270.208 917.333333c-29.824 0-55.04-10.325333-75.690667-30.976A102.954667 102.954667 0 0 1 163.541333 810.666667v-123.050667h128V106.666667h597.333334V810.666667c0 29.824-10.325333 55.04-30.976 75.690666a102.954667 102.954667 0 0 1-75.690667 30.976h-512z m512-64c12.074667 0 22.186667-4.096 30.421333-12.288a41.301333 41.301333 0 0 0 12.245334-30.378666V170.666667h-469.333334v516.949333h384V810.666667c0 12.074667 4.096 22.186667 12.288 30.378666 8.149333 8.192 18.304 12.288 30.378667 12.288zM406.4 370.858667v-64h367.616v64H406.4z m0 123.093333v-64h367.616v64H406.4zM270.208 853.333333h405.333333v-101.76h-448V810.666667c0 12.074667 4.096 22.186667 12.288 30.378666 8.149333 8.192 18.304 12.288 30.378667 12.288z m0 0h-42.666667 448-405.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--checkbox1-->
<view wx:if="{{name === 'checkbox1'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M896 944V1024H128v-80h768z m48-48V128a48 48 0 0 0-48-48H128a48 48 0 0 0-48 48v768c0 26.496 21.504 48 48 48V1024a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128h768a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128v-80a48 48 0 0 0 48-48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Icon-2-->
<view wx:if="{{name === 'Icon-2'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1045 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M461.568 663.168l239.018667-239.018667-37.461334-37.461333-201.557333 201.557333-101.376-101.333333-37.461333 37.461333 138.837333 138.837334z m50.517333 186.538667a329.045333 329.045333 0 0 1-131.754666-26.581334A341.162667 341.162667 0 0 1 273.066667 750.933333a341.162667 341.162667 0 0 1-72.234667-107.221333A328.746667 328.746667 0 0 1 174.250667 512c0-46.72 8.832-90.624 26.581333-131.754667a341.162667 341.162667 0 0 1 72.192-107.264 341.162667 341.162667 0 0 1 107.221333-72.192 328.746667 328.746667 0 0 1 131.712-26.624c46.72 0 90.624 8.874667 131.712 26.581334 41.130667 17.749333 76.885333 41.813333 107.306667 72.192a341.12 341.12 0 0 1 72.192 107.221333c17.749333 41.088 26.624 84.992 26.624 131.712a329.002667 329.002667 0 0 1-26.581333 131.754667 341.162667 341.162667 0 0 1-72.192 107.264 340.992 340.992 0 0 1-107.264 72.234666 328.746667 328.746667 0 0 1-131.669334 26.581334z m-0.085333-53.333334c79.402667 0 146.688-27.52 201.813333-82.645333 55.082667-55.125333 82.645333-122.368 82.645334-201.813333 0-79.36-27.562667-146.645333-82.688-201.728-55.082667-55.125333-122.368-82.688-201.770667-82.688-79.402667 0-146.645333 27.562667-201.770667 82.688-55.125333 55.082667-82.688 122.368-82.688 201.770666 0 79.36 27.562667 146.645333 82.688 201.770667 55.125333 55.082667 122.368 82.645333 201.813334 82.645333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--checkbox-->
<view wx:if="{{name === 'checkbox'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M919.272727 512A407.272727 407.272727 0 1 0 512 919.272727V977.454545C254.929455 977.454545 46.545455 769.070545 46.545455 512S254.929455 46.545455 512 46.545455s465.454545 208.384 465.454545 465.454545-208.384 465.454545-465.454545 465.454545v-58.181818A407.272727 407.272727 0 0 0 919.272727 512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(45,46,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--back-->
<view wx:if="{{name === 'back'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M597.2992 256.00000001l-255.99999999 255.99999999 255.99999999 255.99999999 59.7504-59.75039999L460.8 512l196.2496-196.2496L597.2992 256.00000001z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--shoppingbag-->
<view wx:if="{{name === 'shoppingbag'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1066 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M210.346667 768h601.770666V361.984H210.304V768z m665.770666 28.416l-0.170666 3.626667a35.626667 35.626667 0 0 1-31.786667 31.786666l-3.626667 0.170667H181.888a35.626667 35.626667 0 0 1-35.413333-32l-0.170667-3.584V333.568c0-19.626667 15.957333-35.541333 35.584-35.584H840.533333l3.626667 0.170667c17.92 1.834667 31.914667 16.981333 31.957333 35.413333v462.848zM648.32 245.418667a21.418667 21.418667 0 0 0-21.418667-21.418667H395.52a21.418667 21.418667 0 0 0-21.418667 21.418667v8.874666h-64v-8.874666c0-47.146667 38.272-85.418667 85.418667-85.418667h231.381333c47.189333 0 85.418667 38.272 85.418667 85.418667v8.874666h-64v-8.874666z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--customDetail-->
<view wx:if="{{name === 'customDetail'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M520.96 499.626667c-44.928 0-83.413333-16-115.413333-48S357.504 381.098667 357.504 336.128c0-44.928 16-83.413333 48.042667-115.413333 32-32 70.485333-48.042667 115.413333-48.042667 44.970667 0 83.456 16 115.456 48.042667 32 32 48.042667 70.485333 48.042667 115.413333 0 44.970667-16 83.456-48.042667 115.498667-32 32-70.485333 48-115.456 48zM170.666667 855.338667v-103.850667c0-22.869333 6.229333-44.032 18.645333-63.573333 12.416-19.456 29.013333-34.432 49.792-44.928a677.717333 677.717333 0 0 1 139.733333-50.944 604.8 604.8 0 0 1 284.288 0c46.976 11.349333 93.568 28.330667 139.733334 50.944 20.778667 10.496 37.376 25.472 49.792 44.970666 12.416 19.498667 18.645333 40.661333 18.645333 63.530667v103.850667H170.666667z m70.058666-70.058667H801.28v-33.792c0-9.472-2.730667-18.218667-8.234667-26.282667a62.336 62.336 0 0 0-22.314666-19.712 600.704 600.704 0 0 0-123.093334-45.056 532.266667 532.266667 0 0 0-253.226666 0c-41.813333 10.24-82.858667 25.258667-123.093334 45.056a62.293333 62.293333 0 0 0-22.314666 19.712 45.781333 45.781333 0 0 0-8.234667 26.282667v33.792z m280.234667-355.712c25.728 0 47.701333-9.130667 66.005333-27.434667 18.261333-18.304 27.434667-40.277333 27.434667-66.005333 0-25.685333-9.173333-47.658667-27.434667-65.962667a89.941333 89.941333 0 0 0-65.962666-27.434666c-25.728 0-47.701333 9.130667-66.005334 27.434666a89.941333 89.941333 0 0 0-27.434666 65.962667c0 25.728 9.130667 47.701333 27.434666 66.005333 18.304 18.261333 40.277333 27.434667 65.962667 27.434667z' fill='{{(isStr ? colors : colors[0]) || 'rgb(45,46,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--address-->
<view wx:if="{{name === 'address'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 696.5248c68.2496-56.1664 119.4496-110.08 153.6-161.5872 34.1504-51.5584 51.2-99.584 51.2-144.128 0-61.6448-18.9952-112.2816-57.088-152.064C621.6704 199.1168 572.4672 179.2 512 179.2c-60.416 0-109.6704 19.9168-147.712 59.7504C326.2464 278.7328 307.2 329.216 307.2 390.3488c0 44.1344 17.0496 91.8016 51.2 143.0016 34.1504 51.2 85.3504 105.5744 153.6 163.1744z m0 97.0752c-95.2832-71.8336-166.0416-141.0048-212.2752-207.5648-46.2336-66.56-69.3248-131.7888-69.3248-195.6352 0-83.9168 26.112-152.8832 78.4384-206.9504C361.1648 129.4336 428.9024 102.4 512.1024 102.4c83.0976 0 150.8352 27.0336 203.1104 81.0496C767.4368 237.568 793.6 306.4832 793.6 390.4c0 63.8464-23.0912 129.024-69.3248 195.584-46.2336 66.56-116.992 135.7824-212.2752 207.616z m0.2048-332.8c21.1968 0 39.2704-7.5264 54.2208-22.6304 14.8992-15.0528 22.3744-33.2288 22.3744-54.3744 0-21.1968-7.5264-39.2704-22.6304-54.2208A74.5472 74.5472 0 0 0 511.7952 307.2c-21.1968 0-39.2704 7.5264-54.2208 22.6304A74.5472 74.5472 0 0 0 435.2 384.2048c0 21.1968 7.5264 39.2704 22.6304 54.2208 15.0528 14.8992 33.2288 22.3744 54.3744 22.3744zM230.4 921.6v-76.8h563.2V921.6h-563.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--order-->
<view wx:if="{{name === 'order'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M256 900.352c-22.7328 0-41.8816-7.8336-57.5488-23.552a78.2848 78.2848 0 0 1-23.552-57.5488v-148.6336h102.4V123.8016h571.7504v682.6496c0 26.2656-9.0624 48.4864-27.2384 66.6624a90.624 90.624 0 0 1-66.6112 27.2384H256z m498.944-55.5008c10.8544 0 19.968-3.6864 27.4432-11.008a36.864 36.864 0 0 0 11.2128-27.392V179.2512h-460.8v491.3664H716.8v135.8336c0 10.9056 3.6352 19.968 10.9568 27.392a36.864 36.864 0 0 0 27.1872 11.008zM392.192 370.944V315.4944h342.016v55.4496H392.192z m0 123.0848V438.528h342.016V494.08H392.192zM256 844.8h405.2992v-118.784H230.4V819.2c0 7.2704 2.4576 13.312 7.3728 18.2784A24.7808 24.7808 0 0 0 256 844.8z m0 0h-25.6 430.8992H256z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--scan-->
<view wx:if="{{name === 'scan'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M184.192 896.085333c-22.058667 0-40.533333-7.424-55.338667-22.186666-14.805333-14.805333-22.186667-33.28-22.186666-55.338667v-162.474667h48v162.432c0 7.424 3.072 14.165333 9.216 20.352a28.245333 28.245333 0 0 0 20.309333 9.216h162.474667v48H184.192z m450.474667 0v-48h162.474666a28.245333 28.245333 0 0 0 20.309334-9.216 28.245333 28.245333 0 0 0 9.216-20.352v-162.432H874.666667v162.432c0 22.101333-7.381333 40.533333-22.186667 55.381334-14.805333 14.762667-33.28 22.186667-55.338667 22.186666h-162.474666zM106.666667 368.085333V205.610667c0-22.058667 7.381333-40.533333 22.186666-55.338667 14.805333-14.805333 33.28-22.186667 55.338667-22.186667h162.474667v48H184.192a28.245333 28.245333 0 0 0-20.309333 9.216 28.245333 28.245333 0 0 0-9.216 20.309334v162.474666H106.666667z m720 0V205.610667a28.245333 28.245333 0 0 0-9.216-20.309334 28.245333 28.245333 0 0 0-20.309334-9.216h-162.474666V128.085333h162.474666c22.058667 0 40.533333 7.381333 55.338667 22.186667 14.805333 14.805333 22.186667 33.28 22.186667 55.338667v162.474666h-48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(45,46,44)'}}' /%3E%3Cpath d='M874.666667 818.517333v-162.432h-48v162.432l-0.597334 5.418667a30.08 30.08 0 0 1-8.618666 14.933333l-4.736 4.010667a27.392 27.392 0 0 1-10.154667 4.608l-5.418667 0.597333v-4.266666a24.021333 24.021333 0 0 0 17.28-7.978667 24.021333 24.021333 0 0 0 7.978667-17.28v-166.741333H878.933333v166.741333c0 23.125333-7.808 42.709333-23.466666 58.368-15.616 15.616-35.2 23.424-58.325334 23.424h-166.741333v-56.533333h166.741333v4.266666h-162.474666v48h162.474666l8.106667-0.341333a73.813333 73.813333 0 0 0 47.232-21.845333c12.928-12.970667 20.224-28.714667 21.845333-47.232l0.341334-8.106667z m-720 0v-162.432H106.666667v162.432l0.341333 8.149334c1.621333 18.517333 8.917333 34.261333 21.845333 47.232 14.805333 14.762667 33.28 22.186667 55.338667 22.186666h162.474667v-48H184.192v-4.266666h166.741333v56.533333H184.192c-23.125333 0-42.709333-7.808-58.368-23.466667-15.658667-15.616-23.424-35.2-23.424-58.325333v-166.741333h56.533333v166.741333c0 6.058667 2.474667 11.776 7.978667 17.28a24.021333 24.021333 0 0 0 17.28 7.978667v4.266666a26.325333 26.325333 0 0 1-15.573333-5.205333l-4.693334-4.010667a30.122667 30.122667 0 0 1-8.661333-14.933333l-0.597333-5.418667z m0-612.906666c0-7.381333 3.072-14.165333 9.216-20.309334a28.245333 28.245333 0 0 1 20.309333-9.216h162.474667V128.085333H184.192v-4.266666h166.741333v56.533333H184.192a24.021333 24.021333 0 0 0-17.28 7.978667 24.021333 24.021333 0 0 0-7.978667 17.28v166.741333H102.4V205.610667c0-23.125333 7.765333-42.709333 23.466667-58.368 15.616-15.658667 35.2-23.424 58.325333-23.424v4.266666c-22.058667 0-40.533333 7.381333-55.338667 22.186667l-5.205333 5.717333c-11.306667 13.738667-16.981333 30.293333-16.981333 49.621334v162.474666h48V205.610667z m720 0c0-19.328-5.674667-35.84-16.981334-49.621334l-5.205333-5.717333a73.856 73.856 0 0 0-47.232-21.845333l-8.106667-0.341334v-4.266666c23.125333 0 42.709333 7.765333 58.368 23.466666 15.658667 15.616 23.424 35.2 23.424 58.325334v166.741333h-56.533333V205.610667a24.021333 24.021333 0 0 0-7.978667-17.28 24.021333 24.021333 0 0 0-17.28-7.978667h-166.741333V123.818667h166.741333v4.266666h-162.474666v48h162.474666c7.381333 0 14.165333 3.072 20.309334 9.216a28.245333 28.245333 0 0 1 9.216 20.309334v162.474666H874.666667V205.610667zM250.666667 663.936V358.485333h43.648v305.493334H250.666667z m65.493333 0V358.485333h43.605333v305.493334H316.16z m65.450667 0V358.485333h21.802666v305.493334h-21.802666z m65.450666 0V358.485333H490.666667v305.493334h-43.648z m65.450667 0V358.485333h65.450667v305.493334h-65.450667z m87.253333 0V358.485333h21.845334v305.493334h-21.845334z m65.450667 0V358.485333h65.450667v305.493334h-65.450667z' fill='{{(isStr ? colors : colors[1]) || 'rgb(45,46,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--customMade-->
<view wx:if="{{name === 'customMade'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M824.533333 277.333333H853.333333c39.253333 0 71.082667 31.829333 71.082667 71.082667v71.125333c0 39.253333-31.829333 71.125333-71.082667 71.125334h-28.672l0.213334 263.082666H853.333333c39.253333 0 71.082667 31.872 71.082667 71.125334v99.541333H99.541333v-99.541333c0-39.253333 31.829333-71.125333 71.125334-71.125334h341.333333V597.333333a99.541333 99.541333 0 0 0-99.584-99.584H278.229333L256 597.333333H199.082667v56.874667h-42.666667V597.333333H99.541333V263.082667A71.68 71.68 0 0 1 170.666667 199.082667h28.416V142.208h42.666666v56.874667H640V142.208h-49.792v-42.666667h142.208v42.666667H682.666667v56.874667h71.082666c36.864 0 67.2 28.074667 70.784 64V277.333333z m-42.325333-8.576c-1.578667-15.872-13.781333-27.008-28.458667-27.008H170.666667a28.458667 28.458667 0 0 0-28.330667 25.6L142.208 554.666667h79.573333l22.272-99.584h168.362667A142.208 142.208 0 0 1 554.666667 597.333333v149.333334h227.541333V268.757333z m-640 612.992h739.541333v-56.874666a28.458667 28.458667 0 0 0-28.416-28.458667H170.666667a28.458667 28.458667 0 0 0-28.458667 28.458667v56.874666z m682.666667-561.749333v128H853.333333a28.458667 28.458667 0 0 0 28.416-28.458667V348.416A28.458667 28.458667 0 0 0 853.333333 320h-28.458666z m-156.458667 135.082667a71.125333 71.125333 0 1 1 0-142.208 71.125333 71.125333 0 0 1 0 142.208z m0-42.666667a28.416 28.416 0 1 0 0-56.832 28.416 28.416 0 0 0 0 56.832zM597.333333 568.874667v-42.666667h142.208v42.666667H597.333333z m0 106.666666v-42.666666h142.208v42.666666H597.333333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M924.416 824.874667c0-36.821333-27.989333-67.114667-63.829333-70.741334l-7.253334-0.384v-8.533333c43.946667 0.042667 79.616 35.669333 79.616 79.658667v108.074666H91.008v-108.074666c0-43.946667 35.669333-79.616 79.658667-79.616v8.533333l-7.253334 0.341333a71.125333 71.125333 0 0 0-63.872 70.741334v99.541333h824.874667v-99.541333z m-51.2 0a20.010667 20.010667 0 0 0-19.882667-19.925334H170.666667a20.010667 20.010667 0 0 0-19.925334 19.925334v48.384h722.517334v-48.384z m51.2-405.333334V348.416c0-36.821333-27.989333-67.114667-63.829333-70.741333L853.333333 277.333333v-8.533333c43.946667 0 79.616 35.626667 79.616 79.616v71.125333c0 43.946667-35.626667 79.616-79.616 79.616h-20.138666l0.170666 246.101334H853.333333v8.533333h-28.501333l-0.170667-263.125333H853.333333l7.253334-0.384a71.125333 71.125333 0 0 0 63.829333-70.741334zM503.466667 597.333333a91.050667 91.050667 0 0 0-91.050667-91.050666H285.098667l-20.778667 92.885333-1.493333 6.656H207.616v56.917333h-59.733333V605.866667H91.050667V262.186667A80.256 80.256 0 0 1 170.666667 190.592v8.533333l-6.826667 0.298667c-33.706667 3.2-60.586667 29.994667-64.298667 63.658667V597.333333h56.874667v56.874667h42.666667V597.333333H256l22.229333-99.584h134.186667A99.541333 99.541333 0 0 1 512 597.333333v156.416H170.666667v-8.533333h332.8V597.333333z m270.208-328.021333c-1.28-11.562667-9.984-19.029333-19.925334-19.029333H170.666667a19.968 19.968 0 0 0-19.84 17.621333l-0.085334 278.186667h64.213334l20.778666-92.842667 1.493334-6.656h175.189333v8.533333H244.053333L221.866667 554.666667H142.208l0.128-287.36a28.458667 28.458667 0 0 1 23.082667-25.045334L170.666667 241.749333h583.082666c14.634667 0 26.88 11.136 28.458667 27.008v477.866667H554.666667V597.333333a142.208 142.208 0 0 0-142.250667-142.208v-8.533333A150.741333 150.741333 0 0 1 563.157333 597.333333v140.8h210.517334V269.312z m-34.133334 363.562667H597.333333v42.666666h142.208v-42.666666z m0-106.666667H597.333333v42.666667h142.208v-42.666667z m0-142.208c0-36.821333-27.989333-67.114667-63.829333-70.741333l-7.253333-0.384C629.12 312.874667 597.333333 344.704 597.333333 384l0.341334 7.253333a71.125333 71.125333 0 0 0 141.482666 0l0.384-7.253333z m133.717334 35.541333V348.416A20.010667 20.010667 0 0 0 853.333333 328.490667h-19.925333v110.933333H853.333333v8.533333h-28.458666v-128H853.333333a28.458667 28.458667 0 0 1 28.416 28.458667v71.125333a28.458667 28.458667 0 0 1-8.32 20.096l-4.266666 3.584a28.288 28.288 0 0 1-4.949334 2.56l-5.333333 1.706667a28.842667 28.842667 0 0 1-5.546667 0.512v-8.533333a20.010667 20.010667 0 0 0 19.925334-19.925334zM688.341333 384a20.053333 20.053333 0 0 0-12.288-18.432 19.968 19.968 0 1 0-7.637333 38.357333v8.533334a28.501333 28.501333 0 0 1-15.786667-4.778667l-4.309333-3.584a28.458667 28.458667 0 0 1-7.765333-14.549333L640 384c0-5.674667 1.664-11.136 4.736-15.786667l3.584-4.352A28.458667 28.458667 0 0 1 696.874667 384l-0.554667 5.546667a28.245333 28.245333 0 0 1-1.621333 5.333333l-2.602667 4.906667a28.586667 28.586667 0 0 1-12.8 10.496l-5.333333 1.578666a28.458667 28.458667 0 0 1-5.546667 0.554667v-8.533333A19.968 19.968 0 0 0 688.341333 384z m136.192-120.917333a71.125333 71.125333 0 0 0-70.784-64v-8.533334c41.301333 0 75.264 31.445333 79.232 71.68l0.042667 0.426667V268.8H853.333333v8.533333h-28.8v-14.250666z m-92.117333-120.874667v-42.666667h-142.208v42.666667H640v56.874667H241.749333V142.208h-42.666666v56.874667H170.666667v-8.533334h19.925333V133.717333h59.733333v56.917334H631.466667v-39.850667h-49.792v-59.733333h159.274666v59.733333H691.2v39.850667h62.592v8.533333H682.666667V142.165333h49.749333z m149.333333 739.541333H142.208v-56.874666c0-1.877333 0.170667-3.712 0.554667-5.546667l1.621333-5.333333a28.458667 28.458667 0 0 1 2.602667-4.906667l3.541333-4.309333a28.458667 28.458667 0 0 1 14.549333-7.808l5.546667-0.554667h682.666667a28.458667 28.458667 0 0 1 28.458666 28.458667v56.874666z m-133.674666-197.674666H588.8v-59.733334h159.274667v59.733334z m0-106.666667H588.8v-59.733333h159.274667v59.733333z m0-193.408a79.658667 79.658667 0 1 1-159.317334-0.085333A79.658667 79.658667 0 0 1 748.074667 384z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--store-->
<view wx:if="{{name === 'store'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M600.277333 661.376a113.066667 113.066667 0 0 0 83.029334-34.304 113.066667 113.066667 0 0 0 34.304-83.029333c0-32.469333-11.434667-60.16-34.304-83.029334A113.066667 113.066667 0 0 0 600.277333 426.666667c-32.512 0-60.16 11.434667-83.029333 34.304a113.152 113.152 0 0 0-34.304 83.029333c0 32.469333 11.434667 60.16 34.304 83.029333a113.066667 113.066667 0 0 0 83.029333 34.304z m214.869334 128l-117.76-118.570667c-14.208 10.88-29.44 19.157333-45.696 24.746667a155.818667 155.818667 0 0 1-51.413334 8.490667c-44.501333 0-82.261333-15.530667-113.365333-46.634667-31.104-31.146667-46.634667-68.906667-46.634667-113.365333s15.530667-82.261333 46.634667-113.365334c31.104-31.104 68.864-46.634667 113.365333-46.634666 44.458667 0 82.261333 15.530667 113.365334 46.634666 31.061333 31.146667 46.634667 68.906667 46.634666 113.365334 0 18.005333-2.858667 35.114667-8.576 51.413333a164.693333 164.693333 0 0 1-24.917333 45.738667l118.826667 117.973333-30.464 30.208z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M760.277333 544.042667c0-41.685333-13.653333-77.525333-41.002666-107.477334l-5.632-5.888c-31.146667-31.104-68.906667-46.634667-113.365334-46.634666-44.501333 0-82.261333 15.530667-113.365333 46.634666-31.104 31.146667-46.634667 68.906667-46.634667 113.365334s15.530667 82.261333 46.634667 113.365333a153.514667 153.514667 0 0 0 105.088 46.506667l8.277333 0.128c13.482667 0 26.496-1.578667 39.04-4.736l12.373334-3.712a162.261333 162.261333 0 0 0 45.738666-24.789334l117.76 118.570667 30.421334-30.208-118.826667-117.973333c8.192-10.666667 14.933333-21.930667 20.138667-33.706667l4.778666-12.032c5.717333-16.298667 8.533333-33.408 8.533334-51.413333z m-51.2 0a104.533333 104.533333 0 0 0-31.786666-77.013334 104.576 104.576 0 0 0-77.013334-31.786666c-30.250667 0-55.765333 10.538667-77.013333 31.786666a104.576 104.576 0 0 0-31.786667 77.013334c0 30.250667 10.538667 55.722667 31.786667 77.013333a104.533333 104.533333 0 0 0 77.013333 31.786667v8.533333l-11.946666-0.554667a111.104 111.104 0 0 1-62.293334-25.685333l-8.789333-8.064a113.066667 113.066667 0 0 1-34.304-83.029333c0-32.469333 11.434667-60.16 34.304-83.029334A113.109333 113.109333 0 0 1 600.277333 426.666667c32.469333 0 60.16 11.434667 83.029334 34.304s34.304 50.517333 34.304 83.029333a113.066667 113.066667 0 0 1-34.304 83.029333 113.066667 113.066667 0 0 1-83.029334 34.304v-8.533333a104.533333 104.533333 0 0 0 76.970667-31.786667l7.466667-8.192c16.298667-19.541333 24.32-42.368 24.32-68.821333z m59.733334 0a162.56 162.56 0 0 1-30.933334 96.128l113.706667 112.938666 6.144 6.058667-42.624 42.282667-5.973333-6.058667-112.725334-113.493333c-13.184 9.258667-27.093333 16.64-41.941333 21.76-17.194667 5.973333-35.285333 8.96-54.186667 8.96-46.72 0-86.656-16.469333-119.381333-49.194667-32.725333-32.725333-49.152-72.661333-49.152-119.381333s16.426667-86.656 49.152-119.381334c32.725333-32.725333 72.661333-49.152 119.381333-49.152s86.613333 16.426667 119.381334 49.152c32.725333 32.725333 49.152 72.661333 49.152 119.381334z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M192 789.333333V309.333333L512 68.48l320 240.853333v87.893334a209.152 209.152 0 0 0-31.274667-6.357334A267.648 267.648 0 0 0 768 388.906667V341.333333l-256-192.853333L256 341.333333v384h221.44a234.197333 234.197333 0 0 0 18.218667 64H192z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--close-->
<view wx:if="{{name === 'close'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M273.066667 795.946667l-44.970667-44.970667 238.933333-238.933333-238.933333-238.933334L273.066667 228.181333l238.933333 238.933334 238.933333-238.933334L795.904 273.066667l-238.933333 238.933333 238.933333 238.933333-44.970667 44.970667-238.933333-238.933333-238.933333 238.933333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--timeLine-->
<view wx:if="{{name === 'timeLine'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M324.266667 423.68H145.066667v51.2h179.2v-51.2zM324.266667 551.68H145.066667v51.2h179.2v-51.2zM324.266667 679.68H145.066667v51.2h179.2v-51.2z' fill='{{(isStr ? colors : colors[0]) || 'rgb(45,46,44)'}}' /%3E%3Cpath d='M1009.92 718.890667a322.133333 322.133333 0 0 0-57.173333-158.293334 328.533333 328.533333 0 0 0-128-109.226666 178.773333 178.773333 0 0 0-14.08-237.226667 177.493333 177.493333 0 0 0-166.4-48.213333 185.173333 185.173333 0 0 0-72.106667 34.56 178.346667 178.346667 0 0 0-29.866667 249.173333 333.696 333.696 0 0 0-105.813333 81.066667v-167.253334A51.157333 51.157333 0 0 0 384 312.277333H85.333333a52.48 52.48 0 0 0-36.266666 14.933334 52.48 52.48 0 0 0-14.933334 36.266666v426.666667c0.085333 13.568 5.461333 26.538667 14.933334 36.266667 9.685333 9.472 22.698667 14.805333 36.266666 14.933333h298.666667a51.2 51.2 0 0 0 51.2-51.2v-12.8h530.346667c6.186667 0 12.288-1.28 17.92-3.84a46.08 46.08 0 0 0 15.36-10.24 46.933333 46.933333 0 0 0 8.96-15.36 48.64 48.64 0 0 0 2.986666-17.493333l-0.853333-11.52zM375.466667 781.610667H93.866667v-409.6h281.6v409.6z m59.733333-64v-76.8a272.213333 272.213333 0 0 1 65.706667-92.16 264.192 264.192 0 0 1 98.986666-58.026667 29.866667 29.866667 0 0 0 14.933334-9.813333 31.146667 31.146667 0 0 0 8.106666-15.786667v-20.48l-3.413333-2.56-9.386667-7.68a115.2 115.2 0 0 1-32.853333-42.666667 116.48 116.48 0 0 1 0-102.826666 124.16 124.16 0 0 1 32.426667-42.666667 130.986667 130.986667 0 0 1 48.64-23.04 128 128 0 0 1 53.76 0c22.144 5.546667 42.24 17.194667 58.026666 33.706667a119.466667 119.466667 0 0 1 34.986667 78.506666v6.826667a119.893333 119.893333 0 0 1-42.666667 91.733333 34.986667 34.986667 0 0 0-12.8 32l6.4 19.626667h4.266667l11.946667 3.84a263.253333 263.253333 0 0 1 128 89.6 267.52 267.52 0 0 1 54.613333 135.253333l-519.68 3.413334z' fill='{{(isStr ? colors : colors[1]) || 'rgb(45,46,44)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--Icon-->
<view wx:if="{{name === 'Icon'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M748.8 496.768L612.522667 360.533333l45.610666-44.544L748.8 406.613333l181.333333-181.333333 44.970667 45.653333-226.304 225.834667zM384 498.816c-41.088 0-76.202667-14.592-105.472-43.861333C249.301333 425.728 234.666667 390.528 234.666667 349.482667s14.634667-76.202667 43.861333-105.429334C307.797333 214.784 342.954667 200.106667 384 200.106667c41.088 0 76.202667 14.634667 105.472 43.904 29.226667 29.226667 43.861333 64.384 43.861333 105.429333 0 41.088-14.634667 76.245333-43.861333 105.472-29.269333 29.269333-64.426667 43.861333-105.472 43.861333z m-320 324.949333v-94.848c0-20.906667 5.674667-40.277333 17.024-58.026666a113.578667 113.578667 0 0 1 45.525333-41.088 618.965333 618.965333 0 0 1 127.573334-46.506667 552.533333 552.533333 0 0 1 259.712 0c42.922667 10.325333 85.461333 25.813333 127.658666 46.506667 18.944 9.557333 34.133333 23.253333 45.482667 41.045333 11.349333 17.792 17.024 37.162667 17.024 58.026667v94.890666h-640z m64-64h512v-30.848a41.813333 41.813333 0 0 0-7.509333-24.021333 56.874667 56.874667 0 0 0-20.394667-18.005333 548.309333 548.309333 0 0 0-112.426667-41.130667 486.229333 486.229333 0 0 0-231.338666 0c-38.186667 9.301333-75.690667 23.04-112.426667 41.130667-8.576 4.693333-15.36 10.666667-20.394667 18.005333a41.813333 41.813333 0 0 0-7.509333 24.021333v30.848z m256-324.949333c23.466667 0 43.52-8.32 60.245333-25.045333 16.725333-16.725333 25.088-36.821333 25.088-60.288s-8.362667-43.52-25.088-60.245334A82.176 82.176 0 0 0 384 264.106667c-23.466667 0-43.52 8.362667-60.245333 25.088A82.176 82.176 0 0 0 298.666667 349.44c0 23.466667 8.362667 43.562667 25.088 60.288 16.682667 16.725333 36.778667 25.045333 60.245333 25.045333z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />