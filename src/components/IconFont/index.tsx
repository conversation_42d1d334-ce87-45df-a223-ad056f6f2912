/* tslint:disable */
/* eslint-disable */

import React, { FunctionComponent } from 'react';

export type IconNames = 'content_copy' | 'event_available' | 'openCatalog' | 'closeCatalog' | 'filter' | 'check' | 'call' | 'sortBy' | 'distance' | 'search' | 'logo' | 'Successful' | 'delete' | 'bianji' | 'checked1' | 'rules' | 'checked' | 'detail' | 'productList' | 'error' | 'avatar' | 'mic' | 'setting' | 'note' | 'orderList' | 'checkbox1' | 'Icon-2' | 'checkbox' | 'back' | 'shoppingbag' | 'customDetail' | 'address' | 'order' | 'scan' | 'customMade' | 'store' | 'close' | 'timeLine' | 'Icon';

export interface IconProps {
  name: IconNames;
  size?: number;
  color?: string | string[];
  style?: React.CSSProperties;
}

const IconFont: FunctionComponent<IconProps> = () => {
  return null;
};

export default IconFont;
