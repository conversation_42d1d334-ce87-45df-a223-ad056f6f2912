import { View } from "@tarojs/components";
import { Input } from "@nutui/nutui-react-taro";

import React, { ReactNode, useState } from "react";
import IconFont from "../iconfont";
import sty from "./index.module.scss";
import Taro from "@tarojs/taro";

interface SearchInputType {
  placeholder: string;
  onChange: (v: string) => void;
  onConfirm?: (v: string) => void;
  confirmType?: "search";
  children?: React.ReactNode;
  clearable?: boolean;
  richtIcon?: ReactNode;
}

const SearchInput: React.FC<SearchInputType> = ({ placeholder, onChange, onConfirm = () => {}, confirmType, children, clearable, richtIcon }) => {
  const [val, setVal] = useState("");

  return (
    <View className="flex items-center w-full  mx-8">
      <View className="flex items-center border-1-area bg-brand-FAFAFA w-full py-5 px-8 rounded-sm">
        <IconFont name="search" size={20} />
        <Input
          className={["text-sm leading-8 pl-4 bg-brand-FAFAFA w-full", sty.search_input].join(" ")}
          type="text"
          style={sty.search_input}
          placeholder={placeholder}
          value={val}
          onChange={(value) => {
            setVal(value);
            onChange(value); // 实时触发搜索
          }}
          confirmType="search"
          onConfirm={(e) => {
            onConfirm(e.detail.value);
          }}
          clearable={clearable}
          clearIcon={<IconFont name="close" size={16} />}
        />
      </View>
      {richtIcon && <View >{richtIcon}</View>}
    </View>
  );
};

export default SearchInput;
