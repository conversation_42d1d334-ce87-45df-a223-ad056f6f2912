import { View } from "@tarojs/components";
import { Image } from "@nutui/nutui-react-taro";
import { OrderItem, OrderGoods } from "@/mobx/model/Order";

const OrderCard: React.FC<OrderItem> = (props) => {
  return (
    <View className={`orderCardCom w-130 border-1-area py-8 ${props.class}`}>
      <View className="flex justify-between items-center leading-8 text-sm px-6 ">
        <View className=" text-brand-2D2E2C">Orders: {props.order_sn}</View>
        <View className="text-brand-898989 font-medium">Completed</View>
      </View>
      <View className="leading-8 mt-2 text-brand-8C8C8C flex items-center text-sm  px-6">
        <View>2023/11/13</View> &nbsp;
        <View> 16:23:43</View>
      </View>
      <View className="relative">
        <View className="overflow-x-scroll pl-6 box-border mt-5 ">
          <View className="flex ">
            {props.goods?.map((good, index) => (
              <View className="w-38 h-51 min-w-38 min-h-51 mr-2" key={index}>
                {/* <Image /> */}
                <Image key={good.goods_sn} src={good.goods_img} />
              </View>
            ))}
          </View>
        </View>
        <View
          className={["absolute w-43  h-51 top-0 right-0  box-border  pl-11 flex justify-center items-center text-brand-C33333 text-sm"].join(" ")}
          style={{
            background: "linear-gradient(269.26deg, #FAFAFA 61.35%, rgba(255, 255, 255, 0) 100.23%)",
          }}>
          Exchange
        </View>
      </View>

      <View className="px-6 flex justify-between items-end text-sm leading-8 mt-5">
        <View>
          <View className="text-brand-898989">Qty.2</View>
          <View className="text-brand-2D2E2C mt-2">¥6360</View>
        </View>
        <View className="py-4 px-6 border-1-area text-brand-2D2E2C">View Details</View>
      </View>
    </View>
  );
};
export default OrderCard;
