import { View } from "@tarojs/components";
import classNames from "classnames";
import { observer } from "mobx-react";
import React, { forwardRef, ReactNode, useEffect, useImperativeHandle, useRef, useState } from "react";

import { Loading, HeaderBar, Toast as CustomToast } from "@/components";
import { HeaderBarProps, HeaderBarType } from "@/components/HeaderBar";
import MainLayoutContext from "@/contexts/mainLayout";
import { useStores, useTaroSid, useHeaderBarHeight, useSafeAreaBottom } from "@/hook";
import { ShareAppMessageReturn } from "@tarojs/taro";
import "./index.scss";

//加载优化选项
interface InitOptions {
  //是否可以加载显示
  inited: boolean;
  //加载完过一些时间再展示 优化体验
  initDelay?: number;
  //未加载及Delay时是否继续展示loading
  initLoading?: boolean;
  //是否全屏
  loadingFullScreen?: boolean;
}

interface MainLayoutProps {
  // HeaderBar 相关配置
  showHeaderBar?: boolean;
  headBarConfig?: HeaderBarProps;

  // 便捷的 HeaderBar 配置选项（会覆盖 headBarConfig 中的对应属性）
  headerTitle?: string;
  headerType?: HeaderBarType;
  headerBackgroundColor?: string;
  headerColor?: string;
  showBack?: boolean;
  leftIcon?: React.ReactNode;
  onBackClick?: () => void;

  // 其他配置
  showTopPlaceholder?: boolean;
  showBottomPlaceholder?: boolean;
  //断网后刷新触发 一般不需要传
  onErrorRefresh?: () => void;
  shareAppMessage?: ShareAppMessageReturn;
  //加载优化选项
  initOptions?: Partial<InitOptions>;
  children?: ReactNode;
  className?: string;
  bgColor?: string;
  style?: React.CSSProperties;
}

const MainLayout = forwardRef((props: MainLayoutProps, ref) => {
  // 设置默认值
  const { showHeaderBar = true, showBottomPlaceholder = true } = props;
  const { loadingStore, toastStore } = useStores();
  //统一管理placeholder防止多个dix计算困难
  const { ref: layoutRef, select, selectAll } = useTaroSid();
  const { headerBarHeight } = useHeaderBarHeight();
  const { safeAreaBottom } = useSafeAreaBottom();

  // 合并 HeaderBar 配置
  const getHeaderBarConfig = (): HeaderBarProps => {
    const { headBarConfig = {}, headerTitle, headerType, headerBackgroundColor, headerColor, leftIcon, showBack: propsShowBack, onBackClick } = props;

    // 便捷配置会覆盖 headBarConfig 中的对应属性
    return {
      ...headBarConfig,
      ...(headerTitle !== undefined && { headerTitle }),
      ...(headerType !== undefined && { type: headerType }),
      ...(headerBackgroundColor !== undefined && { backgroundColor: headerBackgroundColor }),
      ...(headerColor !== undefined && { color: headerColor }),
      ...(leftIcon !== undefined && { leftIcon }),
      ...(propsShowBack !== undefined && { showBack: propsShowBack }),
      ...(onBackClick !== undefined && { onBackClick }),
    };
  };

  //初始化完成后再将页面可见 还可以设置delay防止图片多变形太大
  const initLoadingUUID = useRef<string>("");

  // 移除页面级别的登录检查，改为只在接口 401 时跳转登录

  useEffect(() => {
    const { inited, initDelay, initLoading, loadingFullScreen } = {
      inited: true,
      initDelay: 0,
      initLoading: false,
      loadingFullScreen: false,
      ...props.initOptions,
    };

    if (inited) {
      // 页面已初始化完成
      if (initDelay && initDelay > 0) {
        // 有延迟设置，先关闭loading，延迟后再次关闭确保清理
        if (initLoading && initLoadingUUID.current) {
          loadingStore.close(initLoadingUUID.current);
        }
        setTimeout(() => {
          if (initLoading && initLoadingUUID.current) {
            loadingStore.close(initLoadingUUID.current);
          }
        }, initDelay);
      } else {
        // 无延迟，直接关闭loading并显示页面
        if (initLoading && initLoadingUUID.current) {
          loadingStore.close(initLoadingUUID.current);
        }
      }
    } else {
      // 页面未初始化完成，显示初始化loading
      if (initLoading) {
        initLoadingUUID.current = loadingStore.open({
          isFullScreen: loadingFullScreen,
          isInitLoading: true,
        });
      }
    }
  }, [props.initOptions?.inited, props.initOptions?.initDelay, props.initOptions?.initLoading, props.initOptions?.loadingFullScreen]);

  useEffect(() => {
    return () => {
      loadingStore.close(initLoadingUUID.current);
    };
  }, []);

  const initError = (err: any) => {
    loadingStore.close(initLoadingUUID.current);
    toastStore.show(err || "加载失败");
  };

  // 简单自定义toast
  const commonFunction = {
    select,
    selectAll,
    initError,
  };

  useImperativeHandle(ref, () => commonFunction);

  const style = Object.assign({}, props.style);

  const ToastNode = () => {
    return <CustomToast />;
  };

  return (
    <View
      className={classNames("main_layout")}
      ref={layoutRef}
      style={
        {
          ...style,
          "--headerbar-height": headerBarHeight + "px",
          "--safe-area-bottom": safeAreaBottom + "px",
        } as React.CSSProperties
      }>
      {showHeaderBar ? <HeaderBar {...getHeaderBarConfig()} /> : null}
      <Loading />
      {/* 简单自定义toast */}
      {ToastNode()}
      {/* 静默登录的页面在静默登录之前都不要展示 但还是需要配合useSlientAuthEffect才能保证执行顺序 */}
      <View
        className={classNames("main_layout_content", {
        })}
        style={{
          position: 'absolute',
          top: showHeaderBar ? headerBarHeight : 0,
          left: 0,
          right: 0,
          bottom: showBottomPlaceholder ? safeAreaBottom : 0,
          transition: 'opacity 0.3s ease'
        }}
      >
        <MainLayoutContext.Provider value={commonFunction}>{props.children}</MainLayoutContext.Provider>
      </View>
    </View>
  );
});

export default observer(MainLayout);
