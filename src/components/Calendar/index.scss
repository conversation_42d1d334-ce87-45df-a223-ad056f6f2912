.Calendar {
  width: 100%;
  margin: auto;

  .selectMonth {
    margin: auto;
    margin-bottom: 24PX;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .mr26 {
    font-size: 14PX;
    margin-right: 26PX;
  }

  .ml26 {
    font-size: 14PX;
    margin-left: 26PX;
  }

  .dayItem {
    width: calc(100% / 7);
    height: 32Px;
    margin: 9PX 0;
    line-height: 32PX;
    text-align: center;
    font-family: 'Zona Pro';
    font-style: normal;
    font-weight: 400;
    font-size: 12Px;
    text-align: center;
    letter-spacing: 1Px;
    text-transform: capitalize;
    color: #424242;
  }

  .week {
    border-bottom: 2PX solid #212121;
    padding-bottom: 8PX;

    .weekItem {
      padding: 0 2PX;
      width: 46Px;
      text-align: center;
    }
  }

  .choosed {
    font-weight: 800;
    background: #F5F5F5;

    //灰色的日期格子不可被选中
    &.colorf5 {
      font-weight: 400;
      background: none;
    }
  }

  .colorf5 {
    color: rgba(0, 0, 0, 0.25);
    pointer-events: none;
  }

  .unSelect {
    color: #f3f3f3;
  }
}