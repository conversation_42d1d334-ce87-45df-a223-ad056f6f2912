import React, { useEffect, useState } from 'react'
import { Text, View } from '@tarojs/components'
import Taro from '@tarojs/taro'

import './index.scss'


const Calendar = ({ returnDate }) => {
  const weekArr = [
    '日',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
  ]
  // 12点之前 预约时间节点为一天后开始，过12点，预约时间节点为两天后开始
  const timeH = new Date().getHours() > 12 ? 2 : 1

  const [currentMonth, setMonth] = useState(new Date().getMonth() + 1)
  const [currentYear, setYear] = useState(new Date().getFullYear())
  const [byChooseDay, setByChooseDay] = useState<number>(1)
  const [currentMonthArr, setMonthArr] = useState<number[]>([])
  const [oldDate, setOldDate] = useState(false)
  const [dayNow, setDayNow] = useState(new Date().getDate())
  const [chooseDay, setChooseDay] = useState<number>(new Date().getDate())
  const [chooseMonth, setChooseMonth] = useState<number>(
    new Date().getMonth() + 1
  )
  const [chooseYear, setChooseYear] = useState<number>(new Date().getFullYear())
  //是否是第七个月当月
  const [isSeven, setSeven] = useState(false)
  const oldDay = (year, month) => {
    const yearNow = new Date().getFullYear()
    const monthNow = new Date().getMonth() + 1

    if (year < yearNow) {
      setOldDate(true)
    } else if (year === yearNow && month < monthNow) {
      setOldDate(true)
    } else if (year === yearNow && month > monthNow + 5) {
      setOldDate(true)
    } else if (year > yearNow && month + (12 - monthNow) > 6) {
      setOldDate(true)
    } else {
      setOldDate(false)
    }
    if ((year = yearNow + 1 && month + (12 - monthNow) === 6)) {
      setSeven(true)
    } else {
      setSeven(false)
    }
  }
  // 获取某年某月总共多少天
  const getDateLen = (year, month) => {
    const actualMonth = month - 1
    const timeDistance = +new Date(year, month) - +new Date(year, actualMonth)
    return timeDistance / (1000 * 60 * 60 * 24)
  }
  // 获取某月1号是周几
  const getFirstDateWeek = (year, month) => {
    return new Date(year, month - 1, 1).getDay()
  }
  // 获取当月数据，返回数组
  const getCurrentArr = (year, month) => {
    const currentMonthDateLen = getDateLen(year, month) // 获取当月天数
    const currentMonthDateArr: number[] = [] // 定义空数组
    if (currentMonthDateLen > 0) {
      for (let i = 1; i <= currentMonthDateLen; i++) {
        currentMonthDateArr.push(i)
      }
    }
    return currentMonthDateArr
  }
  // 上月多余数据
  const getPreArr = (year, month) => {
    let preMonthDateLen = getFirstDateWeek(year, month)
    const date: number[] = []
    if (preMonthDateLen === 0) {
      preMonthDateLen = 7
    }
    for (let i = 0; i < preMonthDateLen; i++) {
      date.push(0)
    }
    return date
  }
  // 整合当月所有数据
  const getAllArr = (year, month) => {
    oldDay(year, month)
    const currentArr = getCurrentArr(year, month)
    const preArr = getPreArr(year, month)
    let allArr = [...preArr, ...currentArr]
    const counts = {};
    for (let i = 0; i < allArr.length; i++) {
      const item = allArr[i];
      counts[item] = counts[item] ? counts[item] + 1 : 1;
    }
    if (counts[0] >= 7) {
      allArr = allArr.slice(counts[0], allArr.length)
    }

    setMonthArr(allArr)
  }
  // 上月 年、月
  const preMonth = (year, month) => {
    if (month == 1) {
      return {
        year: --year,
        month: 12,
      }
    } else {
      return {
        year: year,
        month: --month,
      }
    }
  }
  // 下月 年、月
  const nextMonth = (year, month) => {
    if (month == 12) {
      return {
        year: ++year,
        month: 1,
      }
    } else {
      return {
        year: year,
        month: ++month,
      }
    }
  }
  // 点击 上月
  const gotoPreMonth = () => {
    const { year, month } = preMonth(currentYear, currentMonth)
    if (month < new Date().getMonth() + 1 && year <= new Date().getFullYear()) {
      return
    } else {
      setMonth(month)
      setYear(year)
      getAllArr(year, month)
    }

  }
  // 点击 下月
  const gotoNextMonth = () => {
    const { year, month } = nextMonth(currentYear, currentMonth)
    const addMonth = year > new Date().getFullYear() ? month : 0
    if (year >= new Date().getFullYear() && (11 - new Date().getMonth()) + addMonth > 6) {
      return
    } else {
      setMonth(month)
      setYear(year)
      getAllArr(year, month)
    }
  }

  //选择日期
  const selectDay = (item) => {
    if (
      oldDate ||
      (dayNow + timeH > item && currentMonth === new Date().getMonth() + 1)
    ) {
      return
    } else if (isSeven && dayNow < item) {
      return
    } else {
      Taro.setStorageSync('selectAppointDate', [currentYear, currentMonth, item])
      setChooseDay(item)
      setChooseMonth(currentMonth)
      setChooseYear(currentYear)
      returnDate(currentYear + '/' + currentMonth + '/' + item)
    }
  }
  useEffect(() => {
    getAllArr(currentYear, currentMonth)

    if (new Date().getHours() > 12) {
      setByChooseDay(2)
      setChooseDay(chooseDay + 2)
    } else {
      setByChooseDay(1)
      setChooseDay(chooseDay + 1)
    }
    const selectDateStorage = Taro.getStorageSync('selectAppointDate')
    if (selectDateStorage) {
      setChooseDay(selectDateStorage[2])
      setChooseMonth(selectDateStorage[1])
      setChooseYear(selectDateStorage[0])
    }

  }, [])

  // 半年后的切换箭头置灰
  const unSelect = currentYear >= new Date().getFullYear() && (11 - new Date().getMonth()) + (currentYear > new Date().getFullYear() ? currentMonth : 0) > 5
  return (
    <View className='Calendar'>
      <View className='selectMonth'>
        <Text
          className={`${currentMonth === new Date().getMonth() + 1 && 'unSelect'} iconfont icon-zuojiantou mr26`}
          onClick={gotoPreMonth}
        ></Text>
        <Text>{currentMonth}月</Text>
        <Text
          className={`${unSelect ? 'unSelect' : ''} iconfont icon-youjiantou ml26`}
          onClick={gotoNextMonth}
        ></Text>
      </View>
      <View className='fx-sc-c week'>
        {weekArr.map((item, index) => {
          return (
            <View key={index} className='weekItem'>
              {item}
            </View>
          )
        })}
      </View>
      <View className='fx fx-wrap'>
        {currentMonthArr.map((item, index) => {
          return (
            <View
              key={index}
              className={`dayItem ${oldDate
                ? 'colorf5'
                : dayNow + byChooseDay > item && currentMonth === new Date().getMonth() + 1
                  ? 'colorf5'
                  : ''
                }  
                ${isSeven && dayNow < item ? 'colorf5' : ''}
                ${chooseDay === item &&
                  currentYear === chooseYear &&
                  currentMonth === chooseMonth
                  ? 'choosed'
                  : ''
                }`}
              onClick={() => {
                selectDay(item)
              }}
            >
              {item === 0 ? ' ' : item}
            </View>
          )
        })}
      </View>
    </View>
  )
}
export default Calendar
