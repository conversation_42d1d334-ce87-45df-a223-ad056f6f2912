// Mock配置 - 控制哪些接口使用mock，哪些使用真实接口
export interface MockConfig {
  // 全局mock开关
  enabled: boolean;
  // 具体模块的mock控制
  modules: {
    auth: boolean;      // 登录相关接口
    customer: boolean;  // 客户相关接口  
    order: boolean;     // 订单相关接口
    common: boolean;    // 通用接口
    setting: boolean;   // 设置相关接口
  };
  // Mock服务器配置
  mockBaseURL: string;
  // 真实接口配置
  realBaseURL: string;
}

// 根据环境变量获取mock配置
const getMockConfig = (): MockConfig => {
  const apiEnv = process.env.API_ENV;
  const nodeEnv = process.env.NODE_ENV;
  
  // 生产环境禁用所有mock
  if (nodeEnv === 'production') {
    return {
      enabled: false,
      modules: {
        auth: false,
        customer: false,
        order: false,
        common: false,
        setting: false,
      },
      mockBaseURL: '',
      realBaseURL: 'https://pos-sit.ibaiqiu.cn'
    };
  }
  
  // 开发环境的配置
  if (apiEnv === 'mock') {
    // 全局mock模式 - 但可以单独控制某些模块
    return {
      enabled: true,
      modules: {
        auth: false,        // 登录使用真实接口
        customer: false,    // 客户使用真实接口
        order: true,        // 订单使用mock
        common: false,      // 通用接口使用真实接口
        setting: false,     // 设置使用真实接口
      },
      mockBaseURL: 'http://127.0.0.1:9527',
      realBaseURL: 'https://pos-suitsupply.mybaiqiu.com'
    };
  }
  
  // 默认使用真实接口
  return {
    enabled: false,
    modules: {
      auth: false,
      customer: false,
      order: false,
      common: false,
      setting: false,
    },
    mockBaseURL: 'http://127.0.0.1:9527',
    realBaseURL: apiEnv === 'dev' 
      ? 'https://pos-suitsupply.mybaiqiu.com'
      : 'https://pos-suitsupply.ibaiqiu.cn'
  };
};

export const mockConfig = getMockConfig();

// 获取指定模块的baseURL
export const getModuleBaseURL = (module: keyof MockConfig['modules']): string => {
  if (mockConfig.enabled && mockConfig.modules[module]) {
    return mockConfig.mockBaseURL;
  }
  return mockConfig.realBaseURL;
};

// 检查指定模块是否使用mock
export const isModuleMocked = (module: keyof MockConfig['modules']): boolean => {
  return mockConfig.enabled && mockConfig.modules[module];
};
