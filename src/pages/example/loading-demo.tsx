import { View, Button } from '@tarojs/components'
import { observer } from 'mobx-react'
import React from 'react'
import { useStores } from '@/hook'
import MainLayout from '@/components/MainLayout'

const LoadingDemo: React.FC = () => {
  const { loadingStore } = useStores()

  // 普通loading示例
  const showNormalLoading = () => {
    const uuid = loadingStore.open({
      showDelay: 0 // 立即显示
    })
    
    // 3秒后关闭
    setTimeout(() => {
      loadingStore.close(uuid)
    }, 3000)
  }

  // 全屏loading示例
  const showFullScreenLoading = () => {
    const uuid = loadingStore.openFullScreen({
      showDelay: 0 // 立即显示
    })
    
    // 3秒后关闭
    setTimeout(() => {
      loadingStore.close(uuid)
    }, 3000)
  }

  // 延迟显示loading示例
  const showDelayedLoading = () => {
    const uuid = loadingStore.open({
      showDelay: 1000 // 1秒后显示
    })
    
    // 5秒后关闭
    setTimeout(() => {
      loadingStore.close(uuid)
    }, 5000)
  }

  // 初始化loading示例
  const showInitLoading = () => {
    const uuid = loadingStore.open({
      isInitLoading: true,
      isFullScreen: true,
      showDelay: 0
    })
    
    // 3秒后关闭
    setTimeout(() => {
      loadingStore.close(uuid)
    }, 3000)
  }

  return (
    <MainLayout
      headerTitle="Loading组件演示"
      showBack={true}
    >
      <View className="p-4 space-y-4">
        <View className="text-lg font-bold mb-4">Loading组件功能演示</View>
        
        <View className="space-y-3">
          <Button 
            className="w-full bg-blue-500 text-white py-3 rounded"
            onClick={showNormalLoading}
          >
            显示普通Loading (3秒)
          </Button>
          
          <Button 
            className="w-full bg-green-500 text-white py-3 rounded"
            onClick={showFullScreenLoading}
          >
            显示全屏Loading (3秒)
          </Button>
          
          <Button 
            className="w-full bg-orange-500 text-white py-3 rounded"
            onClick={showDelayedLoading}
          >
            延迟1秒显示Loading (总共5秒)
          </Button>
          
          <Button 
            className="w-full bg-purple-500 text-white py-3 rounded"
            onClick={showInitLoading}
          >
            显示初始化Loading (3秒)
          </Button>
        </View>

        <View className="mt-8 p-4 bg-gray-100 rounded">
          <View className="text-base font-semibold mb-2">当前Loading状态:</View>
          <View className="text-sm space-y-1">
            <View>是否显示: {loadingStore.isShow ? '是' : '否'}</View>
            <View>是否全屏: {loadingStore.isFullScreen ? '是' : '否'}</View>
            <View>打开次数: {loadingStore.openTimes}</View>
            <View>Loading列表长度: {loadingStore.loadingList.length}</View>
          </View>
        </View>

        <View className="mt-8 p-4 bg-blue-50 rounded">
          <View className="text-base font-semibold mb-2">使用说明:</View>
          <View className="text-sm space-y-2">
            <View>• Loading组件已在MainLayout中引入，无需在页面中单独引入</View>
            <View>• 通过loadingStore管理Loading状态</View>
            <View>• 支持普通和全屏两种模式</View>
            <View>• 支持延迟显示，避免快速接口的闪烁</View>
            <View>• 支持初始化Loading，用于页面初始化时的加载状态</View>
            <View>• 自动处理页面路径，确保Loading只在当前页面显示</View>
          </View>
        </View>
      </View>
    </MainLayout>
  )
}

export default observer(LoadingDemo)
