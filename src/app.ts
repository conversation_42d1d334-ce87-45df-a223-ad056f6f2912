import { Component, PropsWithChildren } from 'react'
import '@/assets/font/index.scss'
import '@nutui/nutui-react-taro/dist/style.css'
import './app.scss'
import './style/index.scss'

class App extends Component<PropsWithChildren> {

  componentDidMount () {}

  componentDidShow () {}

  componentDidHide () {}

  render () {
    // this.props.children 是将要会渲染的页面
    return this.props.children
  }
}

export default App
