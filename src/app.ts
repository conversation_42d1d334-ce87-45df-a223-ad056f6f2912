import { Component, PropsWithChildren } from 'react'
import '@/assets/font/index.scss'
import '@nutui/nutui-react-taro/dist/style.css'
import './app.scss'
import './style/index.scss'

// 设置NutUI为英文
import { ConfigProvider } from '@nutui/nutui-react-taro'

// 在开发环境下引入mock状态检查工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/mockHelper');
}

class App extends Component<PropsWithChildren> {

  componentDidMount () {}

  componentDidShow () {}

  componentDidHide () {}

  render () {
    // this.props.children 是将要会渲染的页面
    return this.props.children
  }
}

export default App
