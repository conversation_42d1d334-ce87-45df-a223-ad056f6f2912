/* Tailwind CSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 小程序特定的样式调整 */
@layer components {
  /* 自定义组件样式 */

  // 按钮
  .btn-primary {
    @apply bg-primary-500 text-white px-4 py-2 rounded-lg font-medium;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 px-4 py-2 rounded-lg font-medium;
  }

  .btn-black {
    @apply bg-black text-white text-base   font-medium rounded-default w-214 py-6 px-7 leading-10 h-auto box-content;
  }
  .btn-white {
    @apply bg-white text-black text-base  font-medium border-[1px] border-[#E1E1E1] rounded-default  py-6 px-7  leading-10 h-auto break-keep;
  }

  .card {
    @apply bg-white rounded-lg border border-gray-100 p-4;
  }

  .btn-default {
    @apply bg-brand-2D2E2C h-22 flex text-base text-white rounded-default;
  }

  // 边框
  .border-1-area-xs {
    @apply border-[0.5px] border-[#E1E1E1] rounded-default border-solid;
  }
  .border-1-area {
    @apply border-[1px] border-[#E1E1E1] rounded-default border-solid;
  }

  .border-t-1 {
    @apply border-y-0 border-x-0 border-t-[1px] border-t-[#E1E1E1] border-solid;
  }

  .border-b-1 {
    @apply border-y-0 border-x-0 border-b-[1px] border-b-[#E1E1E1] border-solid;
  }

  .border-r-1 {
    @apply border-y-0 border-x-0 border-r-[1px] border-r-[#E1E1E1] border-solid;
  }

  .border-l-1 {
    @apply border-y-0 border-x-0 border-l-[1px] border-l-[#E1E1E1] border-solid;
  }

  .border-t-1-xs {
    @apply border-y-0 border-x-0 border-t-[0.5px] border-t-[#E1E1E1] border-solid;
  }

  .border-b-1-xs {
    @apply border-y-0 border-x-0 border-b-[0.5px] border-b-[#E1E1E1] border-solid;
  }

  .border-r-1-xs {
    @apply border-y-0 border-x-0 border-r-[0.5px] border-r-[#E1E1E1] border-solid;
  }

  .border-l-1-xs {
    @apply border-y-0 border-x-0 border-l-[0.5px] border-l-[#E1E1E1] border-solid;
  }

	.noborder{
		@apply border-0;
	}

  // 输入框
  .input {
    @apply px-8 px-5 my-6 mx-8;
  }

  .input-border {
    @apply w-full h-11  px-4 text-sm font-light text-black bg-white border-1-area;
  }
  .input-error {
    @apply border-[#C33333];
  }
  .input-msg {
    @apply absolute left-4 -top-2 bg-white px-1 text-xs leading-8 z-10 text-brand-898989;
  }
  .input-msg-error {
    @apply text-brand-C33333;
  }
	.input-placeholder{
		@apply text-brand-898989 font-normal text-base leading-10;
	}

	.input-textarea{
		@apply h-40 p-4 border-1-area bg-white text-base text-black;
		border: 1px solid rgba(239, 239, 239, 1);
	}
  // 字体

  .font-gt-america-medium {
    font-family: "GTAmericaMedium", sans-serif;
  }
  .font-gt-america-normal {
    font-family: "GTAmericaRegular", sans-serif;
  }

  // 阴影
  .shadow {
    box-shadow: 0px -2px 12px 0px #0000000a;
  }
	.flex-center{
		@apply flex justify-center items-center;
	}
	.flex-between{
		@apply flex justify-between items-center;
	}
	.flex-around{
		@apply flex justify-around items-center;
	}
	.flex-start{
		@apply flex justify-start items-center;
	}
	.flex-end{
		@apply flex justify-end items-center;
	}
}

@layer utilities {
  /* rpx 单位的工具类 */
  .w-rpx-100 {
    width: 100rpx;
  }

  .h-rpx-100 {
    height: 100rpx;
  }
}
