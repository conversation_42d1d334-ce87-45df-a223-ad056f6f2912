import Taro, { scanCode, createSelectorQuery } from '@tarojs/taro'


import projectConfig from '../../project.config.json'

export const systemInfo: any = Taro.getSystemInfoSync()

// 延迟获取菜单按钮信息，避免在模块加载时调用可能失败的API
let menuButtonReact: any = null
export const getMenuButtonReact = () => {
  if (!menuButtonReact) {
    try {
      menuButtonReact = Taro.getMenuButtonBoundingClientRect()
      // console.log('成功获取菜单按钮信息:', menuButtonReact)
    } catch (error) {
      console.warn('获取菜单按钮信息失败，使用默认值:', error)
      // 返回默认值，避免后续计算出错
      const statusBarHeight = systemInfo.statusBarHeight || 20
      menuButtonReact = {
        top: statusBarHeight + 4,
        bottom: statusBarHeight + 36,
        left: systemInfo.screenWidth - 87,
        right: systemInfo.screenWidth - 7,
        width: 80,
        height: 32
      }
      // console.log('使用默认菜单按钮信息:', menuButtonReact)
    }
  }
  return menuButtonReact
}

export const accountInfo: any = Taro.getAccountInfoSync()

export const getAppID = () => projectConfig.appid

//链接跳转/锚点跳转
export function toJump(_url = '', redirectTo = false) {
  try {
    // console.log('_url', _url)

    if (!_url || _url === '/') return
    const jumpLink = _url.split('||')
    if (jumpLink[1] && jumpLink[0]?.split('wxappid=')) {
      Taro.navigateToMiniProgram({
        path: '/' + jumpLink[1],
        appId: jumpLink[0]?.split('wxappid=')[1],
        envVersion: 'release',
      })
      return
    }
    const url = _url.indexOf('#') >= 0 ? _url : _url
    //tabbar页面
    if ('home'.indexOf(url) !== -1 || 'home'.indexOf('/' + url?.split('?')[0]) !== -1) {
      Taro.switchTab({
        url: url.slice(0, 1) == '/' ? url : '/' + url,
      })
    } else if (url && url.includes('#')) {
      const str = url.split('#')[1]
      if (str) {
        Taro.pageScrollTo({
          selector: `#${str}`,
          duration: 500,
          offsetTop: -130,
        })
      }
    } else {
      let link = ''
      if (url) {
        link = url.replace(/{store_view}/g, 'wx-mp')
      }
      let _url = link.slice(0, 1) == '/' ? link : '/' + link
      if (Taro.getCurrentPages().length > 8) {
        Taro.reLaunch({ url: _url })
      } else if (redirectTo) {
        Taro.redirectTo({ url: _url }).catch(() => {}) //防止sentry报错
      } else {
        Taro.navigateTo({ url: _url }).catch(() => {}) //防止sentry报错
      }
    }
  } catch (error) {
    // console.log('Error in toJump', error)
  }
}

export const backPage = ()=>{
  Taro.navigateBack({
    delta: 1,
  })
}

export const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

const delayPromise = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))
//将异步/同步函数改为异步
const wrapAsyncFunction = async (callback: () => any) => {
  try {
    const result = await Promise.resolve(callback())
    return result
  } catch (error) {
    return Promise.reject(error)
  }
}

//重复执行 默认间隔100,200,200ms
export const repeatQuery = async (callback: () => any, delays: Array<number> = [100, 300, 500]) => {
  for (const delay of delays) {
    await delayPromise(delay)
    await wrapAsyncFunction(callback)
  }
}
export const queryDom = async (param) => {
  return new Promise((resolve) => {
    createSelectorQuery()
      .select(param)
      ?.boundingClientRect()
      .exec((res) => {
        resolve(res && res[0])
      })
  })
}

/**
 * 微信扫码
 *
 */

export const scanCodeFn = async () => {
  return scanCode({
    onlyFromCamera: true,
    success(res) {
      // console.log(res)
      return res
    },
    fail() {
      // console.log('扫码失败')
      return ''
    },
  })
}

// 获取状态栏高度
export const getNavBarInfo = () => {
  try {
    const rect = getMenuButtonReact()

    // 确保 rect 对象存在且有必要的属性
    if (!rect || typeof rect.bottom === 'undefined' || typeof rect.top === 'undefined') {
      console.warn('菜单按钮信息不完整，使用默认导航栏高度')
      const defaultNavBarHeight = 44
      return {
        navBarHeight: defaultNavBarHeight,
        navBarAndStatusBarHeight: defaultNavBarHeight + (systemInfo.statusBarHeight || 20),
      }
    }

    const navBarHeight = rect.bottom - rect.top + (rect.top - (systemInfo.statusBarHeight || 20)) * 2
    // console.log('计算导航栏高度:', {
    //   rect,
    //   statusBarHeight: systemInfo.statusBarHeight,
    //   navBarHeight
    // })

    return {
      navBarHeight: navBarHeight,
      navBarAndStatusBarHeight: navBarHeight + (systemInfo.statusBarHeight || 20),
    }
  } catch (error) {
    console.error('获取导航栏信息失败，使用默认值:', error)
    const defaultNavBarHeight = 44
    return {
      navBarHeight: defaultNavBarHeight,
      navBarAndStatusBarHeight: defaultNavBarHeight + (systemInfo.statusBarHeight || 20),
    }
  }
}

// 跳转到 SuitSupply 定制页面
export const navigateToCustomMade = (params: {
  productCode?: string | string[];
  client?: string;
  product?: string;
  countryCode?: string;
  fabricCode?: string;
  section?: string;
  level?: string;
  [key: string]: any;
}) => {
  const urlParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach(v => urlParams.append(key, v));
      } else {
        urlParams.append(key, String(value));
      }
    }
  });

  const url = `/subpackages/mall/custom-made/index?${urlParams.toString()}`;

  Taro.navigateTo({ url });
}
