// 通用接口的 Mock 数据
module.exports = {
  // 店员信息
  "POST /web/mobile/default.php?c=payment_terminal&m=list": (req, res) => {
    const { access_token } = req.body;
    if (access_token) {
      res.json({
        code: 0,
        msg: "成功",
        data: [
          {
            id: "1",
            name: "设备1"
          },
          {
            id: "2",
            name: "设备2"
          }
        ]
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "/web/mobile/default.php?c=payment_terminal&m=list"
      });
    }
  },

  "POST /web/mobile/default.php?c=store&m=list": (req, res) => {
    const { access_token } = req.body;
    if (access_token) {
      res.json({
        code: 0,
        msg: "成功",
        data: [
          {
            id: "1",
            name: "店铺1"
          },
          {
            id: "2",
            name: "店铺2"
          },
          {
            id: "3",
            name: "店铺3"
          }
        ]
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "/web/mobile/default.php?c=store&m=list"
      });
    }
  },

  "POST /web/mobile/default.php?c=user&m=setting_save": (req, res) => {
    const { access_token } = req.body;
    if (access_token) {
      res.json({
        code: 0,
        msg: "成功",
        data: []
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "/web/mobile/default.php?c=user&m=setting_save"
      });
    } 
  }
};
