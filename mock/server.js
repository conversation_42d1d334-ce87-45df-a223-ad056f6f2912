const express = require('express');
const cors = require('cors');
const path = require('path');
const app = express();

// 清理模块缓存的函数（用于热重载）
function clearModuleCache() {
  const moduleFiles = ['./index.js', './auth.js', './common.js','./custom.js'];
  moduleFiles.forEach(file => {
    const fullPath = path.resolve(__dirname, file);
    delete require.cache[fullPath];
  });
}

// 获取最新的路由配置
// 获取模拟路由
function getMockRoutes() {
  // 清除模块缓存
  clearModuleCache();
  // 返回index模块
  return require('./index');
}

// 启用 CORS
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 动态路由中间件 - 每次请求都获取最新的路由配置
app.use((req, res, next) => {
  const mockRoutes = getMockRoutes();

  // 首先尝试精确匹配（包含查询参数）
  const queryString = Object.keys(req.query).length > 0
    ? '?' + new URLSearchParams(req.query).toString()
    : '';
  const fullRouteKey = `${req.method} ${req.path}${queryString}`;

  // 然后尝试基本路径匹配
  const basicRouteKey = `${req.method} ${req.path}`;

  if (mockRoutes[fullRouteKey]) {
    console.log(`🔄 处理请求 (精确匹配): ${fullRouteKey}`);
    return mockRoutes[fullRouteKey](req, res, next);
  } else if (mockRoutes[basicRouteKey]) {
    console.log(`🔄 处理请求 (路径匹配): ${basicRouteKey}`);
    return mockRoutes[basicRouteKey](req, res, next);
  }

  next();
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    code: 404,
    message: `接口不存在: ${req.method} ${req.path}`,
    available_routes: Object.keys(getMockRoutes())
  });
});

// 初始化时显示所有可用路由
function showAvailableRoutes() {
  const mockRoutes = getMockRoutes();
  console.log('\n📋 可用的 Mock 接口:');
  Object.keys(mockRoutes).forEach(key => {
    console.log(`   ${key}`);
  });
  console.log('');
}

const PORT = 9527;
app.listen(PORT, '127.0.0.1', () => {
  console.log(`🎭 Mock 服务器启动成功: http://127.0.0.1:${PORT}`);
  console.log(`🔥 热重载已启用，修改接口文件后会自动重启`);
  showAvailableRoutes();
});
