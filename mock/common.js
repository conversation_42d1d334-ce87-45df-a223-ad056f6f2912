// 通用接口的 Mock 数据
module.exports = {
  // 店员信息
  "POST /web/mobile/default.php?c=user&m=user_info": (req, res) => {
    const { access_token } = req.body;

    if (access_token) {
      res.json({
        code: 200,
        message: "店员信息成功",
        data: {
          id: "2",
          code: "SH001",
          name: "SH001",
          gender: "0",
          language: "en",
          mobile: "18116253590",
          email: "",
          avatar: null,
          payment: "2",
          zd_id: "3",
          permission: {
            switchGuide: "1",
            totalSales: "1"
          }
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "/web/mobile/default.php?c=user&m=user_info"
      });
    }
  },
  "POST /web/mobile/default.php": (req, res) => {
    const { access_token } = req.body;
    if (access_token) {
      res.json({
        code: 200,
        message: "success",
        data: {}
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "/web/mobile/default.php"
      });
    }
  },

  "POST /web/mobile/default.php?c=user&m=home_yj": (req, res) => {
    res.json({
      code: 0,
      msg: "成功",
      data: {
        total_amount: 22535,
        total_order: 5
      }
    });
  },
  "POST /web/mobile/default.php?c=user&m=logout": (req, res) => {
    res.json({
      code: 0,
      msg: "成功",
      data: []
    });
  },

  "POST /web/mobile/default.php?c=customer&m=member_detail_query": (
    req,
    res
  ) => {
    res.json({
      code: 0,
      message: "注册成功",
      data: {
        uid: "1231",
        mobile: "67d8eab52ca36", //顾客手机号
        first_name: "mia",
        last_name: "mia",
        full_name: "mia",
        email: "",
        avatar: "", // 暂无
        note: "111" // 备注
      }
    });
  },
  "POST /web/mobile/default.php?c=customer&m=member_update": (req, res) => {
    res.json({
      code: 0,
      msg: "会员更新成功!",
      data: []
    });
  }
};
