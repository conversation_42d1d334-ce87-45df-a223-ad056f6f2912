// 通用接口的 Mock 数据
module.exports = {
  // 顾客信息
  "POST /web/mobile/default.php?c=register": (req, res) => {
    const { access_token } = req.body;

    // if (access_token) {
    res.json({
      code: 0,
      message: "注册成功",
      data: {}
    });
    // } else {
    // 	res.status(401).json({
    // 		code: 401,
    // 		message: '/web/mobile/default.php?c=register'
    // 	});
    // }
  },
  // 模糊搜索
  "POST /web/mobile/default.php?c=searchCustomers": (req, res) => {
    const { access_token } = req.body;

    // if (access_token) {
    res.json({
      code: 0,
      message: "注册成功",
      data: [
        {
          uid: "1231",
          mobile: "13555555555",
          first_name: "sky",
          last_name: "sky",
          full_name: "sky",
          email: "",
          avatar: "",
          note: ""
        },
        {
          uid: "1231",
          mobile: "13555555554",
          first_name: "mia",
          last_name: "mia",
          full_name: "mia",
          email: "",
          avatar: "",
          note: ""
        },
        {
          uid: "1231",
          mobile: "13888885555",
          first_name: "guo",
          last_name: "erio",
          full_name: "Erio Guo",
          email: "<EMAIL>",
          avatar: "",
          note: "这个是note"
        }
      ]
    });
    // } else {
    // 	res.status(401).json({
    // 		code: 401,
    // 		message: '/web/mobile/default.php?c=register'
    // 	});
    // }
  },

};
