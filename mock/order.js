// 订单相关的 Mock 数据
module.exports = {
  // 获取订单历史列表
  "GET /web/mobile/default.php?c=order&m=history_list": (req, res) => {
    const { access_token, page = 1, page_size = 10, search } = req.query;

    if (access_token) {
      // 模拟订单数据
      const mockOrders = [
        {
          id: "1",
          order_number: "PS00048229",
          customer_name: "<PERSON>, Jindo<PERSON>",
          customer_phone: "18812436614",
          order_time: "2025-04-22 09:29:58",
          status: "Completed",
          products: [
            {
              id: "1",
              name: "White Blazer",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Blazer",
              price: 3000,
              quantity: 1
            },
            {
              id: "2", 
              name: "White Shirt",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Shirt",
              price: 800,
              quantity: 1
            },
            {
              id: "3",
              name: "Gray Suit",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Suit",
              price: 8000,
              quantity: 1
            },
            {
              id: "4",
              name: "White Pants",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Pants",
              price: 279,
              quantity: 1
            }
          ],
          total_quantity: 4,
          total_amount: 12079
        },
        {
          id: "2",
          order_number: "PS00048230",
          customer_name: "Wang, Xiaoming",
          customer_phone: "13912345678",
          order_time: "2025-04-21 14:15:30",
          status: "Processing",
          products: [
            {
              id: "5",
              name: "Black Suit",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=BlackSuit",
              price: 5500,
              quantity: 1
            },
            {
              id: "6",
              name: "White Shirt",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Shirt",
              price: 800,
              quantity: 2
            }
          ],
          total_quantity: 3,
          total_amount: 7100
        },
        {
          id: "3",
          order_number: "PS00048231",
          customer_name: "Zhang, Wei",
          customer_phone: "15987654321",
          order_time: "2025-04-20 10:45:12",
          status: "Completed",
          products: [
            {
              id: "7",
              name: "Navy Blazer",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=NavyBlazer",
              price: 3200,
              quantity: 1
            }
          ],
          total_quantity: 1,
          total_amount: 3200
        }
      ];

      // 如果有搜索条件，过滤数据
      let filteredOrders = mockOrders;
      if (search) {
        filteredOrders = mockOrders.filter(order => 
          order.order_number.toLowerCase().includes(search.toLowerCase()) ||
          order.customer_name.toLowerCase().includes(search.toLowerCase()) ||
          order.customer_phone.includes(search)
        );
      }

      // 分页处理
      const startIndex = (page - 1) * page_size;
      const endIndex = startIndex + parseInt(page_size);
      const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

      res.json({
        code: 0,
        message: "获取订单历史成功",
        data: {
          list: paginatedOrders,
          total: filteredOrders.length,
          page: parseInt(page),
          page_size: parseInt(page_size),
          has_more: endIndex < filteredOrders.length
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 搜索订单
  "GET /web/mobile/default.php?c=order&m=search": (req, res) => {
    const { access_token, keyword } = req.query;

    if (access_token) {
      // 这里可以复用上面的逻辑，或者单独实现搜索逻辑
      res.json({
        code: 0,
        message: "搜索成功",
        data: {
          list: [],
          total: 0
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 获取订单详情
  "GET /web/mobile/default.php?c=order&m=detail": (req, res) => {
    const { access_token, order_id } = req.query;

    if (access_token && order_id) {
      res.json({
        code: 0,
        message: "获取订单详情成功",
        data: {
          id: order_id,
          order_number: "PS00048229",
          customer_name: "Li, Jindou",
          customer_phone: "18812436614",
          order_time: "2025-04-22 09:29:58",
          status: "Completed",
          products: [
            {
              id: "1",
              name: "White Blazer",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Blazer",
              price: 3000,
              quantity: 1,
              sku: "WB001",
              size: "M"
            }
          ],
          total_quantity: 4,
          total_amount: 12079,
          payment_method: "Credit Card",
          shipping_address: "上海市浦东新区张江高科技园区"
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  },

  // 检索订单
  "POST /web/mobile/default.php?c=order&m=retrieve": (req, res) => {
    const { access_token, order_id } = req.body;

    if (access_token && order_id) {
      res.json({
        code: 0,
        message: "订单检索成功",
        data: {
          success: true,
          message: "订单已成功检索到购物车"
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  }
};
