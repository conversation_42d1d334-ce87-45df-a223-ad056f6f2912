// 订单和商品相关的 Mock 数据
module.exports = {
  // ==================== 商品分类相关接口 ====================

  // 一级分类接口
  "GET /web/mobile/default.php?c=goods&m=category_list": (req, res) => {
    const { access_token } = req.query;
    if (access_token) {
      res.json({
        code: 0,
        data: ['Suits', 'Jackets', 'Shirts', 'Accessories', 'Shoes']
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 二级分类接口
  "GET /web/mobile/default.php?c=goods&m=sub_category_list": (req, res) => {
    const { access_token, category } = req.query;
    if (access_token) {
      const subCategories = {
        'Suits': ['Ties', 'Socks', 'Belts'],
        'Jackets': ['Blazers', 'Coats', 'Vests'],
        'Shirts': ['Dress Shirts', 'Casual Shirts', 'Polo Shirts'],
        'Accessories': ['Ties', 'Pocket Squares', 'Cufflinks'],
        'Shoes': ['Dress Shoes', 'Casual Shoes', 'Boots']
      };

      res.json({
        code: 200,
        data: subCategories[category] || []
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // filter接口
  "GET /web/mobile/default.php?c=goods&m=filter_options": (req, res) => {
    const { access_token, category, sed_category } = req.query;
    if (access_token) {
      res.json({
        code: 0,
        data: {
          color: ['Black', 'Navy', 'Gray', 'Brown', 'White'],
          price: ['980-1679CNY', '1679-3200CNY', '3200-5000CNY', '5000+CNY'],
          season: ['Spring', 'Summer', 'Autumn', 'Winter'],
          material: ['Cotton', 'Wool', 'Polyester', 'Silk', 'Linen'],
          size: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
          style: ['Casual', 'Formal', 'Business', 'Evening']
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 商品搜索接口
  "POST /web/mobile/default.php?c=goods&m=search": (req, res) => {
    const { access_token } = req.body;
    if (access_token) {
      const mockGoods = [
        {
          goods_sn: 'C25120A07',
          goods_name: 'Navy Blue Suit',
          goods_price: 2500,
          goods_img: 'https://via.placeholder.com/300x400/1a1a2e/ffffff?text=Navy+Suit',
          discription: 'Premium navy blue wool suit with modern fit',
          color: 'Navy'
        },
        {
          goods_sn: 'C25120A08',
          goods_name: 'Black Formal Jacket',
          goods_price: 1800,
          goods_img: 'https://via.placeholder.com/300x400/000000/ffffff?text=Black+Jacket',
          discription: 'Classic black formal jacket for business occasions',
          color: 'Black'
        },
        {
          goods_sn: 'C25120A09',
          goods_name: 'Gray Casual Blazer',
          goods_price: 1200,
          goods_img: 'https://via.placeholder.com/300x400/808080/ffffff?text=Gray+Blazer',
          discription: 'Comfortable gray blazer for casual wear',
          color: 'Gray'
        }
      ];

      res.json({
        code: 0,
        data: {
          page: 1,
          page_size: 10,
          total: mockGoods.length,
          list: mockGoods
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 商品扫码查询接口
  "GET /web/mobile/default.php?c=goods&m=scan_query": (req, res) => {
    const { access_token, serial_no } = req.query;
    if (access_token && serial_no) {
      res.json({
        code: 0,
        data: {
          goods_sn: serial_no,
          goods_name: 'Scanned Product',
          goods_price: 1500,
          goods_img: 'https://via.placeholder.com/300x400/4a90e2/ffffff?text=Scanned',
          discription: 'Product found by scanning',
          color: 'Blue',
          serial_no: serial_no,
          size: 'M',
          barcode: '1234567890123'
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  },

  // 商品详情接口
  "GET /web/mobile/default.php?c=goods&m=detail": (req, res) => {
    const { access_token, goods_sn, color } = req.query;
    if (access_token && goods_sn) {
      res.json({
        code: 0,
        data: {
          goods_sn: goods_sn,
          goods_name: 'Premium Suit',
          goods_price: 2500,
          goods_img: 'https://via.placeholder.com/300x400/1a1a2e/ffffff?text=Product',
          discription: 'High-quality premium suit with excellent craftsmanship',
          color: color || 'Navy',
          serial_no: 'SN' + goods_sn,
          size: 'M',
          barcode: '1234567890123',
          detail: {
            'MasterProductId': 'H7326A',
            'Material': 'Egyptian Cotton',
            'Brand': 'SUITSUPPLY',
            'Season': 'All Season'
          },
          detail_img: [
            'https://via.placeholder.com/600x800/1a1a2e/ffffff?text=Detail1',
            'https://via.placeholder.com/600x800/1a1a2e/ffffff?text=Detail2',
            'https://via.placeholder.com/600x800/1a1a2e/ffffff?text=Detail3'
          ],
          cm_url: 'https://via.placeholder.com/400x600/ff6b6b/ffffff?text=Custom',
          '裁剪url': 'https://via.placeholder.com/400x600/4ecdc4/ffffff?text=Cutting'
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  },

  // 商品尺码接口
  "GET /web/mobile/default.php?c=goods&m=size_list": (req, res) => {
    const { access_token, goods_sn, color } = req.query;
    if (access_token && goods_sn) {
      res.json({
        code: 0,
        data: [
          {
            size: 'S',
            barcode: '1234567890001',
            store_stock: 5,
            network_stock: 15,
            cm_url: 'https://via.placeholder.com/400x600/ff6b6b/ffffff?text=S+Custom'
          },
          {
            size: 'M',
            barcode: '1234567890002',
            store_stock: 8,
            network_stock: 20,
            cm_url: 'https://via.placeholder.com/400x600/ff6b6b/ffffff?text=M+Custom'
          },
          {
            size: 'L',
            barcode: '1234567890003',
            store_stock: 3,
            network_stock: 12,
            cm_url: 'https://via.placeholder.com/400x600/ff6b6b/ffffff?text=L+Custom'
          },
          {
            size: 'XL',
            barcode: '1234567890004',
            store_stock: 2,
            network_stock: 8,
            cm_url: 'https://via.placeholder.com/400x600/ff6b6b/ffffff?text=XL+Custom'
          }
        ]
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  },

  // 临店库存接口
  "GET /web/mobile/default.php?c=goods&m=store_stock": (req, res) => {
    const { access_token, barcode } = req.query;
    if (access_token && barcode) {
      res.json({
        code: 0,
        data: {
          code: 1,
          name: 'SUITSUPPLY上海店',
          address: '上海市黄浦区南京东路123号',
          mobile: '021-12345678',
          store_stock: 10
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  },

  // ==================== 订单相关接口 ====================

  // OrderHistory列表 - 按照sit2接口文档格式
  "POST /web/mobile/default.php?c=order&m=history_list": (req, res) => {
    const { access_token, uid, page = 1, page_size = 10, search_input, start_date, end_date } = req.body;

    if (access_token) {
      // 模拟订单数据 - 按照sit2文档格式
      const mockOrders = [
        {
          order_sn: "PS00048229",
          order_time: "2025-04-22 09:29:58",
          order_status: "已发货",
          order_price: 12079,
          order_qty: 4,
          custom: "Li, Jindou",
          goods: [
            {
              goods_sn: "C25120A07",
              goods_name: "Navy Blue Suit",
              goods_price: 3000,
              goods_img: "https://via.placeholder.com/120x160/1a1a2e/ffffff?text=Navy+Suit",
              discription: "Premium navy blue wool suit",
              color: "Navy",
              serial_no: "SN001",
              size: "M",
              barcode: "1234567890001",
              status: "已发货",
              detail: {
                "MasterProductId": "H7326A",
                "Material": "Egyptian Cotton"
              },
              detail_img: [
                "https://via.placeholder.com/600x800/1a1a2e/ffffff?text=Detail1",
                "https://via.placeholder.com/600x800/1a1a2e/ffffff?text=Detail2"
              ],
              cm_url: "https://via.placeholder.com/400x600/ff6b6b/ffffff?text=Custom",
              "裁剪url": "https://via.placeholder.com/400x600/4ecdc4/ffffff?text=Cutting"
            },
            {
              goods_sn: "C25120A08",
              goods_name: "White Dress Shirt",
              goods_price: 800,
              goods_img: "https://via.placeholder.com/120x160/ffffff/000000?text=White+Shirt",
              discription: "Classic white dress shirt",
              color: "White",
              serial_no: "SN002",
              size: "M",
              barcode: "1234567890002",
              status: "已发货",
              detail: {
                "MasterProductId": "H7326B",
                "Material": "Cotton"
              },
              detail_img: [
                "https://via.placeholder.com/600x800/ffffff/000000?text=Detail1"
              ],
              cm_url: "https://via.placeholder.com/400x600/ff6b6b/ffffff?text=Custom",
              "裁剪url": "https://via.placeholder.com/400x600/4ecdc4/ffffff?text=Cutting"
            },
            {
              goods_sn: "C25120A09",
              goods_name: "Gray Blazer",
              goods_price: 8000,
              goods_img: "https://via.placeholder.com/120x160/808080/ffffff?text=Gray+Blazer",
              discription: "Elegant gray blazer for business",
              color: "Gray",
              serial_no: "SN003",
              size: "L",
              barcode: "1234567890003",
              status: "已发货",
              detail: {
                "MasterProductId": "H7326C",
                "Material": "Wool"
              },
              detail_img: [
                "https://via.placeholder.com/600x800/808080/ffffff?text=Detail1"
              ],
              cm_url: "https://via.placeholder.com/400x600/ff6b6b/ffffff?text=Custom",
              "裁剪url": "https://via.placeholder.com/400x600/4ecdc4/ffffff?text=Cutting"
            },
            {
              goods_sn: "C25120A10",
              goods_name: "White Pants",
              goods_price: 279,
              goods_img: "https://via.placeholder.com/120x160/ffffff/000000?text=White+Pants",
              discription: "Classic white dress pants",
              color: "White",
              serial_no: "SN004",
              size: "M",
              barcode: "1234567890004",
              status: "已发货",
              detail: {
                "MasterProductId": "H7326D",
                "Material": "Cotton"
              },
              detail_img: [
                "https://via.placeholder.com/600x800/ffffff/000000?text=Detail1"
              ],
              cm_url: "https://via.placeholder.com/400x600/ff6b6b/ffffff?text=Custom",
              "裁剪url": "https://via.placeholder.com/400x600/4ecdc4/ffffff?text=Cutting"
            }
          ]
        },
        {
          order_sn: "PS00048230",
          order_time: "2025-04-21 14:15:30",
          order_status: "处理中",
          order_price: 7100,
          order_qty: 3,
          custom: "Wang, Xiaoming",
          goods: [
            {
              goods_sn: "C25120A11",
              goods_name: "Black Formal Suit",
              goods_price: 5500,
              goods_img: "https://via.placeholder.com/120x160/000000/ffffff?text=Black+Suit",
              discription: "Premium black formal suit",
              color: "Black",
              serial_no: "SN005",
              size: "L",
              barcode: "1234567890005",
              status: "处理中",
              detail: {
                "MasterProductId": "H7326E",
                "Material": "Wool"
              },
              detail_img: [
                "https://via.placeholder.com/600x800/000000/ffffff?text=Detail1"
              ],
              cm_url: "https://via.placeholder.com/400x600/ff6b6b/ffffff?text=Custom",
              "裁剪url": "https://via.placeholder.com/400x600/4ecdc4/ffffff?text=Cutting"
            }
          ]
        }
      ];

      // 如果有搜索条件，过滤数据
      let filteredOrders = mockOrders;
      if (search_input) {
        filteredOrders = mockOrders.filter(order =>
          order.order_sn.toLowerCase().includes(search_input.toLowerCase()) ||
          order.custom.toLowerCase().includes(search_input.toLowerCase()) ||
          order.goods.some(good =>
            good.goods_sn.toLowerCase().includes(search_input.toLowerCase()) ||
            good.goods_name.toLowerCase().includes(search_input.toLowerCase())
          )
        );
      }

      // 日期过滤
      if (start_date && end_date) {
        filteredOrders = filteredOrders.filter(order => {
          const orderDate = new Date(order.order_time);
          const startDate = new Date(start_date);
          const endDate = new Date(end_date);
          return orderDate >= startDate && orderDate <= endDate;
        });
      }

      // 分页处理
      const startIndex = (page - 1) * page_size;
      const endIndex = startIndex + parseInt(page_size);
      const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

      res.json({
        code: 0,
        message: "获取订单历史成功",
        data: {
          list: paginatedOrders,
          total: filteredOrders.length,
          page: parseInt(page),
          page_size: parseInt(page_size),
          has_more: endIndex < filteredOrders.length
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 搜索订单
  "GET /web/mobile/default.php?c=order&m=search": (req, res) => {
    const { access_token, keyword } = req.query;

    if (access_token) {
      // 这里可以复用上面的逻辑，或者单独实现搜索逻辑
      res.json({
        code: 0,
        message: "搜索成功",
        data: {
          list: [],
          total: 0
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "未授权访问"
      });
    }
  },

  // 获取订单详情
  "GET /web/mobile/default.php?c=order&m=detail": (req, res) => {
    const { access_token, order_id } = req.query;

    if (access_token && order_id) {
      res.json({
        code: 0,
        message: "获取订单详情成功",
        data: {
          id: order_id,
          order_number: "PS00048229",
          customer_name: "Li, Jindou",
          customer_phone: "18812436614",
          order_time: "2025-04-22 09:29:58",
          status: "Completed",
          products: [
            {
              id: "1",
              name: "White Blazer",
              image: "https://via.placeholder.com/120x160/f5f5f5/999?text=Blazer",
              price: 3000,
              quantity: 1,
              sku: "WB001",
              size: "M"
            }
          ],
          total_quantity: 4,
          total_amount: 12079,
          payment_method: "Credit Card",
          shipping_address: "上海市浦东新区张江高科技园区"
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  },

  // 检索订单
  "POST /web/mobile/default.php?c=order&m=retrieve": (req, res) => {
    const { access_token, order_id } = req.body;

    if (access_token && order_id) {
      res.json({
        code: 0,
        message: "订单检索成功",
        data: {
          success: true,
          message: "订单已成功检索到购物车"
        }
      });
    } else {
      res.status(400).json({
        code: 400,
        message: "缺少必要参数"
      });
    }
  }
};
